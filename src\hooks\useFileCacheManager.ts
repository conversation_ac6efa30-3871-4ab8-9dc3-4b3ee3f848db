
import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { 
  setPersonalFiles, 
  setProjectFiles, 
  setTrackerProjects,
  setFetchStatus,
  selectIsCacheValid 
} from '@/stores/fileSlice';
import { getFilesByFolderType } from '@/services/api/audioTranscript';
import { getUniqueTrackerProjects } from '@/services/api/audioTranscript/trackerService';
import { REFRESH_TRACKER_PROJECTS_EVENT } from '@/components/dashboard/fileUpload/TrackerDialog';
import { toast } from 'sonner';

// Store fetched data in sessionStorage as backup
const storeInSession = (key: string, data: any) => {
  try {
    sessionStorage.setItem(key, JSON.stringify({
      timestamp: Date.now(),
      data
    }));
  } catch (error) {
    console.warn('Failed to store data in sessionStorage:', error);
  }
};

// Get data from sessionStorage if available and not expired
const getFromSession = <T>(key: string, maxAge = 3600000): { data: T | null; expired: boolean } => {
  try {
    const stored = sessionStorage.getItem(key);
    if (!stored) return { data: null, expired: true };
    
    const { timestamp, data } = JSON.parse(stored);
    const expired = Date.now() - timestamp > maxAge;
    
    return { data: expired ? null : data, expired };
  } catch (error) {
    console.warn('Failed to retrieve data from sessionStorage:', error);
    return { data: null, expired: true };
  }
};

export const useFileCacheManager = () => {
  const dispatch = useAppDispatch();
  
  // Get file data and fetch status from Redux
  const { 
    personalFiles, 
    projectFiles, 
    trackerProjects,
    fetchStatus 
  } = useAppSelector(state => state.file);
  
  // Check if cache is valid based on timestamps
  const isPersonalCacheValid = useAppSelector(state => selectIsCacheValid(state, 'personalFiles'));
  const isProjectCacheValid = useAppSelector(state => selectIsCacheValid(state, 'projectFiles'));
  const isTrackerCacheValid = useAppSelector(state => selectIsCacheValid(state, 'trackerProjects'));
  
  // Fetch personal files with caching
  const fetchPersonalFiles = useCallback(async (forceRefresh = false) => {
    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && isPersonalCacheValid && personalFiles.length > 0) {
      console.log('Using cached personal files');
      return personalFiles;
    }
    
    // Check if already loading
    if (fetchStatus.personalFiles === 'loading') {
      console.log('Personal files fetch already in progress');
      return personalFiles;
    }
    
    // Try session storage if Redux cache is empty
    if (!forceRefresh && personalFiles.length === 0) {
      const sessionData = getFromSession<string[]>('personalFiles');
      if (sessionData.data && !sessionData.expired) {
        console.log('Using session storage for personal files');
        dispatch(setPersonalFiles(sessionData.data));
        return sessionData.data;
      }
    }
    
    // Fetch fresh data
    dispatch(setFetchStatus({ type: 'personalFiles', status: 'loading' }));
    
    try {
      console.log('Fetching personal files from API');
      const data = await getFilesByFolderType('personal');
      dispatch(setPersonalFiles(data));
      storeInSession('personalFiles', data);
      return data;
    } catch (error) {
      console.error('Error fetching personal files:', error);
      dispatch(setFetchStatus({ type: 'personalFiles', status: 'failed' }));
      toast.error('Failed to load personal files');
      return personalFiles;
    }
  }, [dispatch, personalFiles, isPersonalCacheValid, fetchStatus.personalFiles]);
  
  // Fetch project files with caching
  const fetchProjectFiles = useCallback(async (forceRefresh = false) => {
    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && isProjectCacheValid && projectFiles.length > 0) {
      console.log('Using cached project files');
      return projectFiles;
    }
    
    // Check if already loading
    if (fetchStatus.projectFiles === 'loading') {
      console.log('Project files fetch already in progress');
      return projectFiles;
    }
    
    // Try session storage if Redux cache is empty
    if (!forceRefresh && projectFiles.length === 0) {
      const sessionData = getFromSession<string[]>('projectFiles');
      if (sessionData.data && !sessionData.expired) {
        console.log('Using session storage for project files');
        dispatch(setProjectFiles(sessionData.data));
        return sessionData.data;
      }
    }
    
    // Fetch fresh data
    dispatch(setFetchStatus({ type: 'projectFiles', status: 'loading' }));
    
    try {
      console.log('Fetching project files from API');
      const data = await getFilesByFolderType('project');
      dispatch(setProjectFiles(data));
      storeInSession('projectFiles', data);
      return data;
    } catch (error) {
      console.error('Error fetching project files:', error);
      dispatch(setFetchStatus({ type: 'projectFiles', status: 'failed' }));
      toast.error('Failed to load project files');
      return projectFiles;
    }
  }, [dispatch, projectFiles, isProjectCacheValid, fetchStatus.projectFiles]);
  
  // Fetch tracker projects with caching
  const fetchTrackerProjects = useCallback(async (forceRefresh = false) => {
    // Don't update state if the component is unmounting
    let isMounted = true;
    
    try {
      dispatch(setFetchStatus({ type: 'trackerProjects', status: 'loading' }));
      
      // Check if we have valid cached data first
      const sessionData = getFromSession<Array<{ id: string; name: string; fileCount: number }>>('trackerProjects');
      if (!forceRefresh && sessionData.data && !sessionData.expired && sessionData.data.length > 0) {
        if (isMounted) {
          dispatch(setTrackerProjects(sessionData.data));
          dispatch(setFetchStatus({ type: 'trackerProjects', status: 'succeeded' }));
        }
        return sessionData.data;
      }
      
      // Only fetch from API if cache is empty or invalid
      console.log('Fetching tracker projects from API');
      const projectNames = await getUniqueTrackerProjects();
      
      // Transform project names to the expected format with file counts
      const trackerProjectsData = projectNames.map((name, index) => ({
        id: `project-${index}`,
        name,
        fileCount: 0 // Will be updated when we have the actual counts
      }));
      
      if (isMounted) {
        dispatch(setTrackerProjects(trackerProjectsData));
        dispatch(setFetchStatus({ type: 'trackerProjects', status: 'succeeded' }));
        storeInSession('trackerProjects', trackerProjectsData);
      }
      return trackerProjectsData;
    } catch (error) {
      console.error('Error fetching tracker projects:', error);
      if (isMounted) {
        dispatch(setFetchStatus({ type: 'trackerProjects', status: 'failed' }));
      }
      toast.error('Failed to load tracker projects');
      return [];
    }
    
    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [dispatch]);
  
  // Setup event listener for refresh events
  useEffect(() => {
    const handleRefreshEvent = () => {
      console.log('Refresh event received, updating tracker projects');
      fetchTrackerProjects(true);
    };
    
    window.addEventListener(REFRESH_TRACKER_PROJECTS_EVENT, handleRefreshEvent);
    
    return () => {
      window.removeEventListener(REFRESH_TRACKER_PROJECTS_EVENT, handleRefreshEvent);
    };
  }, [fetchTrackerProjects]);
  
  // Combined fetch function for initial loading
  const fetchAllFileData = useCallback(async (forceRefresh = false) => {
    const results = await Promise.all([
      fetchPersonalFiles(forceRefresh),
      fetchProjectFiles(forceRefresh),
      fetchTrackerProjects(forceRefresh)
    ]);
    
    return {
      personalFiles: results[0],
      projectFiles: results[1],
      trackerProjects: results[2]
    };
  }, [fetchPersonalFiles, fetchProjectFiles, fetchTrackerProjects]);
  
  return {
    fetchPersonalFiles,
    fetchProjectFiles,
    fetchTrackerProjects,
    fetchAllFileData,
    fileData: {
      personalFiles,
      projectFiles,
      trackerProjects
    },
    fetchStatus
  };
};
