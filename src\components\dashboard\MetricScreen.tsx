
"use client"

import React from "react"
import { <PERSON>, ChevronR<PERSON>, ChevronLeft } from "lucide-react"
import { useAppDispatch, useAppSelector } from "@/hooks/useRedux"
import { DefineStep } from "./metric/DefineStep"
import { SelectStep } from "./metric/SelectStep"
import { ReviewStep } from "./metric/ReviewStep"
import { ApproveStep } from "./metric/ApproveStep"
import { PublishStep } from "./metric/PublishStep"
import { ProgressSteps } from "./metric/ProgressSteps"
import { SuccessDialog } from "@/components/ui/success-dialog"
import { 
  handleNext, 
  handleBack, 
  handleCloseSuccessMessage, 
  updateFormData, 
  handleTableSelect, 
  setShowSuccessMessage,
  setCurrentStep,
  setShowMetricScreen,
  setPublishCompleted
} from "@/stores/metricSlice"

interface MetricScreenProps {
  onClose: () => void
}

const MetricScreen: React.FC<MetricScreenProps> = ({ onClose }) => {
  const dispatch = useAppDispatch()
  const { 
    currentStep, 
    showSuccessMessage, 
    formData, 
    publishCompleted
  } = useAppSelector(state => state.metric)

  const isFirstStep = currentStep === "define"
  const isLastStep = currentStep === "publish"

  const handleNextStep = () => {
    dispatch(handleNext())
  }

  const handlePrevStep = () => {
    dispatch(handleBack())
  }

  const handleCloseSuccess = () => {
    dispatch(handleCloseSuccessMessage())
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    console.log(`MetricScreen input change: ${name} = ${value}`)
    dispatch(updateFormData({ name, value }))
  }

  const handleTableSelectChange = (tableName: string) => {
    dispatch(handleTableSelect(tableName))
  }

  const handleCloseMetricScreen = () => {
    // Reset the state before closing
    dispatch(setCurrentStep('define'))
    dispatch(setPublishCompleted(false))
    dispatch(setShowSuccessMessage(false))
    dispatch(setShowMetricScreen(false))
    onClose()
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      {/* Header with close button */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Create New Metric</h2>
        <button 
          onClick={handleCloseMetricScreen} 
          className="p-1 rounded-full hover:bg-gray-100 transition-colors" 
          aria-label="Close"
        >
          <X size={20} />
        </button>
      </div>

      {/* Progress Steps */}
      <ProgressSteps currentStep={currentStep} publishCompleted={publishCompleted} />

      {/* Success Message Dialog */}
      <SuccessDialog 
        open={showSuccessMessage} 
        onOpenChange={(open) => dispatch(setShowSuccessMessage(open))}
        onClose={handleCloseSuccess}
        title="Success!"
        message="Your metric has been successfully created and published!"
      />

      {/* Step Content */}
      <div className="mb-8">
        {currentStep === "define" && 
          <DefineStep 
            formData={formData} 
            handleInputChange={handleInputChange} 
          />
        }
        {currentStep === "select" && 
          <SelectStep 
            formData={formData} 
            handleTableSelect={handleTableSelectChange} 
          />
        }
        {currentStep === "review" && 
          <ReviewStep 
            formData={formData} 
            handleInputChange={handleInputChange} 
          />
        }
        {currentStep === "approve" && 
          <ApproveStep 
            formData={formData} 
            handleInputChange={handleInputChange} 
          />
        }
        {currentStep === "publish" && <PublishStep />}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <button onClick={handleCloseMetricScreen} className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50">
          Close
        </button>
        
        <div className="flex space-x-2">
          {!isFirstStep && (
            <button
              onClick={handlePrevStep}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50 flex items-center"
            >
              <ChevronLeft size={16} className="mr-1" /> Back
            </button>
          )}

          {!isLastStep && (
            <button
              onClick={handleNextStep}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
            >
              Next <ChevronRight size={16} className="ml-1" />
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default MetricScreen
