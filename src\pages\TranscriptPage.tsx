import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { TranscriptTab } from '@/components/dashboard/models';
import TranscriptTabNavigation from '@/components/dashboard/fileUpload/TranscriptTabNavigation';
import TranscriptTabView from '@/components/dashboard/fileUpload/TranscriptTabView';
import { useAppSelector, useAppDispatch } from '@/hooks/useRedux';
import { getTranscriptByFileName } from '@/services/api/audioTranscript';
import { setCurrentTranscript } from '@/stores/fileSlice';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { TranscriptItem } from '@/components/dashboard/fileUpload/types';

const TranscriptPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TranscriptTab>('transcript');
  const [isLoading, setIsLoading] = useState(true);
  const { folderType, fileName } = useParams<{ folderType: string; fileName: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const currentTranscript = useAppSelector(state => state.file.currentTranscript);
  
  const [transcriptData, setTranscriptData] = useState<{
    transcript: TranscriptItem[];
    file_id?: number;
    summary: string;
    actionItems: { action: string; dueDate: string; responsible: string }[];
    importantPoints: string[];
    openQuestions: { question: string }[];
  }>({
    transcript: [],
    file_id: undefined,
    summary: '',
    actionItems: [],
    importantPoints: [],
    openQuestions: []
  });

  // Validate folder type
  const isValidFolderType = (type: string | undefined): type is 'personal' | 'project' => {
    return type === 'personal' || type === 'project';
  };

  useEffect(() => {
    const loadTranscript = async () => {
      if (!fileName || !folderType || !isValidFolderType(folderType)) {
        navigate('/transcript');
        return;
      }

      try {
        setIsLoading(true);
        const response = await getTranscriptByFileName(fileName, folderType);
        
        if (response) {
          setTranscriptData({
            transcript: Array.isArray(response.transcript) 
              ? response.transcript 
              : [],
            file_id: response.file_id,
            summary: response.summary.summary,
            actionItems: Array.isArray(response.summary.actionItems)
              ? response.summary.actionItems.map(item => {
                  if (typeof item === 'string') {
                    return { action: item, dueDate: '', responsible: '' };
                  }
                  return item;
                })
              : [],
            importantPoints: response.summary.importantPoints,
            openQuestions: Array.isArray(response.summary.openQuestions)
              ? response.summary.openQuestions.map(q => {
                  if (typeof q === 'string') {
                    return { question: q };
                  }
                  return q;
                })
              : []
          });
          
          // Update Redux store
          dispatch(setCurrentTranscript({
            data: response,
            fileName,
            folderType,
            isExisting: true
          }));
        } else {
          toast.error('Transcript not found');
          navigate('/transcript');
        }
      } catch (error) {
        console.error('Error loading transcript:', error);
        toast.error('Failed to load transcript');
        navigate('/transcript');
      } finally {
        setIsLoading(false);
      }
    };

    loadTranscript();
  }, [fileName, folderType, navigate, dispatch]);

  // Calculate available tabs based on content
  const availableTabs = [
    'transcript',
    ...(transcriptData.summary ? ['summary'] : []),
    ...(transcriptData.importantPoints.length > 0 ? ['notes'] : []),
    ...(transcriptData.actionItems.length > 0 ? ['actionItems'] : []),
    ...(transcriptData.openQuestions.length > 0 ? ['openQuestions'] : [])
  ] as TranscriptTab[];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-semibold text-gray-800">{fileName}</h1>
        </div>
        
        <TranscriptTabNavigation 
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          availableTabs={availableTabs}
          fileName={fileName || ''}
          transcriptData={transcriptData}
          isSharedView={false}
        />

        <TranscriptTabView 
          activeTab={activeTab}
          transcriptData={transcriptData}
        />
      </div>
    </div>
  );
};

export default TranscriptPage;





