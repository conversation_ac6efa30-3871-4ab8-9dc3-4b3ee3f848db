const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const selfsigned = require('selfsigned');

// Generate a self-signed certificate
console.log('Generating self-signed certificate...');

const attrs = [{ name: 'commonName', value: '10.100.0.17' }];
const pems = selfsigned.generate(attrs, { days: 365 });

// Write the certificate and key to files
fs.writeFileSync('cert.pem', pems.cert);
fs.writeFileSync('key.pem', pems.private);

console.log('Certificate generated successfully!');
console.log('cert.pem and key.pem files have been created.');