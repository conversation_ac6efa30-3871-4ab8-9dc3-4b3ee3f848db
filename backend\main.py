from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pinecone_and_embedding_handler import <PERSON>coneHand<PERSON>, Embedding<PERSON>and<PERSON>
from qa_service import QuestionAnswerService
from dotenv import load_dotenv
from application_log import LogService, generate_error_code
from sqlalchemy import text
from typing import Optional
from fastapi import Query
import os
import uvicorn
import pandas as pd
import time
import traceback
import socket
from fastapi.responses import StreamingResponse
import io
from pydantic import BaseModel

# Load environment variables from .env file
load_dotenv()

# Retrieve API keys from environment variables
pinecone_apikey = os.getenv('PINECONE_API_KEY')
openai_apikey = os.getenv('OPENAI_API_KEY')

# Define constants for index and namespaces
INDEX_NAME = 'postgres-sample-test-northwind-db'
PUBS_NAMESPACE = 'healthcare db namespace'
PUBS_QUERY_NAMESPACE = 'pubs query namespace'
questions_db = 'questions_db'
healthcare_db = 'healthcare_test_db'

# Initialize FastAPI app
app = FastAPI(title="Question Answer Service", description="A service to store and retrieve questions and answers")

# Add CORS middleware to allow cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Pinecone handlers
pinecone_handler = PineconeHandler(api_key=pinecone_apikey, index_name=INDEX_NAME, namespace=PUBS_NAMESPACE)
pubs_query_pinecone_handler = PineconeHandler(api_key=pinecone_apikey, index_name=INDEX_NAME, namespace=PUBS_QUERY_NAMESPACE)

# Initialize Embedding handler
embedding_handler = EmbeddingHandler()

# Initialize Question Answer services
qa_service = QuestionAnswerService(pinecone_handler, embedding_handler)
pubs_query_qa_service = QuestionAnswerService(pubs_query_pinecone_handler, embedding_handler)

question_engine= qa_service.postgres_connection_string(questions_db)

log_service = LogService()

# Pydantic model for the request
class QueryRequest(BaseModel):
    query: str

async def process_query_helper(nl_question: str):
    """
    Helper function to process a query and return the result.
    """
    start_time = time.time()

    try:
        is_present_in_vector_db = pubs_query_qa_service.get_similar_questions(nl_question)
        
        if is_present_in_vector_db['matches']:
            sql_query = handle_results(is_present_in_vector_db, nl_question)
            print(sql_query)
            corrected_sql_query, result = execute_query(sql_query, healthcare_db)
        else:
            sql_query = handle_low_score(nl_question)
            print(sql_query)
            corrected_sql_query, result = execute_query(sql_query, healthcare_db)
        pubs_query_qa_service.add_to_pinecone(nl_question, corrected_sql_query)
                
        log_service.insert_log(
            log_level='INFO',
            event_type='QueryProcessed',
            message=f"Processed query: {nl_question} with SQL: {corrected_sql_query} and result rows: {len(result)}",
            user_id=None,
            app_version="1.0",
            ip_address=socket.gethostbyname(socket.gethostname()),
            host_name=socket.gethostname(),
            duration = time.time() - start_time
        )
        return corrected_sql_query, result
    
    except Exception as e:
        # Log the error
        error_code = generate_error_code(e)
        stack_trace = traceback.format_exc()
        
        log_service.insert_log(
            log_level='ERROR',
            event_type='QueryError',
            message=f"Error processing query: {nl_question}. Error: {str(e)}",
            error_code=error_code,
            stack_trace=stack_trace,
            duration = time.time() - start_time
        )
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/query")
async def process_query(nl_question: str):
    """
    Process a query and return the result.
    """
    corrected_sql_query, result = await process_query_helper(nl_question)
    json_result = get_json_data(result, corrected_sql_query)
    return json_result

@app.post("/api/export")
async def export_to_csv(nl_question: str):
    """
    Export the result of a query to a CSV file.
    """
    corrected_sql_query, result = await process_query_helper(nl_question)
    
    # Convert DataFrame to CSV
    csv_buffer = io.StringIO()
    result.to_csv(csv_buffer, index=False)
    csv_buffer.seek(0)
    
    return StreamingResponse(csv_buffer, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=result.csv"})

@app.post("/api/regenerate")
async def regenerate_query(nl_question: str):
    """
    Regenerate the SQL query and return the result.
    """
    start_time = time.time()

    try:
        sql_query = handle_low_score(nl_question)
        corrected_sql_query, result = execute_query(sql_query, healthcare_db)
        pubs_query_qa_service.add_to_pinecone(nl_question, corrected_sql_query)
        
        log_service.insert_log(
            log_level='INFO',
            event_type='QueryRegenerated',
            message=f"Regenerated query: {nl_question} with SQL: {corrected_sql_query} and result rows: {len(result)}",
            user_id=None,
            app_version="1.0",
            ip_address=socket.gethostbyname(socket.gethostname()),
            host_name=socket.gethostname(),
            duration = time.time() - start_time
        )
        json_result = get_json_data(result, corrected_sql_query)
        return json_result
    
    except Exception as e:
        error_code = generate_error_code(e)
        stack_trace = traceback.format_exc()
        
        log_service.insert_log(
            log_level='ERROR',
            event_type='RegenerateError',
            message=f"Error regenerating query: {nl_question}. Error: {str(e)}",
            error_code=error_code,
            stack_trace=stack_trace,
            duration = time.time() - start_time
        )
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/predict")
async def predict_next_words(
    query: str,
    power_keyword: Optional[str] = Query(None, description="Selected Power Keyword"),
    command: Optional[str] = Query(None, description="Selected Command"),
    parameter: Optional[str] = Query(None, description="Selected Parameter"),
):
    """
    Get prediction suggestions based on the query and optional parameters.
    This endpoint supports chained predictions for power keywords, commands, and parameters.
    """
    print(f"Received prediction request with: query={query}, power_keyword={power_keyword}, command={command}, parameter={parameter}")

    # Handle power keyword chains - starting with @@
    if query.startswith("@@"):
        # Step 1: Return Power Keywords when only @@ is entered
        if not power_keyword and not command and not parameter:
            power_keywords = await suggest_power_keywords()
            return [f"@@{keyword}" for keyword in power_keywords]
            
        # Step 2: Return Commands for the selected Power Keyword
        elif power_keyword and not command and not parameter:
            commands = await suggest_commands(power_keyword)
            return [f"@@{power_keyword} {command}" for command in commands]
            
        # Step 3: Return Parameters for the selected Command
        elif power_keyword and command and not parameter:
            parameters = await suggest_parameters(power_keyword, command)
            return [f"@@{power_keyword} {command} {parameter}" for parameter in parameters]
            
        # Step 4: Return the final query
        elif power_keyword and command and parameter:
            final_queries = await suggest_query(power_keyword, command, parameter)
            return final_queries

    # Regular query prediction for non-power keyword queries
    elif not query.startswith("@@") and len(query.strip()) >= 3:
        user_input = query.lower()
        
        # Query the database for similar questions
        engine = qa_service.postgres_connection_string("questions_db")
        question_query = """
            SELECT question
            FROM questions
            WHERE question ILIKE :query
            ORDER BY question
            LIMIT 10
        """
        
        with engine.connect() as connection:
            result = connection.execute(text(question_query), {"query": f"%{user_input}%"})
            rows = result.fetchall()
            suggestions = [row[0] for row in rows]
            
            if suggestions:
                return suggestions
            
            # Fallback to similar questions from vector DB if no direct matches
            result = pubs_query_qa_service.get_similar_questions(query)
            matches = []
            
            if result['matches']:
                for match in result['matches']:
                    if match['score'] > 0.8:
                        matches.append(match['metadata']['question'])
                
            return matches if matches else ["No suggestions found"]
    
    # Return empty for queries that don't meet minimum criteria  
    return []

async def suggest_power_keywords():
    """Fetches and returns a list of distinct power keywords."""
    cur = question_engine.connect()
    result = cur.execute(text("SELECT DISTINCT power_keyword FROM keywords"))
    power_keywords = result.fetchall()
    cur.close()
    return [keyword[0] for keyword in power_keywords]

async def suggest_commands(power_keyword: str):
    """Fetches and returns a list of distinct commands for a given power keyword."""
    cur = question_engine.connect()
    result = cur.execute(text("SELECT DISTINCT command FROM keywords WHERE power_keyword = :keyword"), {"keyword": f'{power_keyword}'})
    commands = result.fetchall()
    cur.close()
    return [command[0] for command in commands]

async def suggest_parameters(power_keyword: str, command: str):
    """Fetches and returns a list of distinct parameters for a given power keyword and command."""
    cur = question_engine.connect()
    result = cur.execute(text("SELECT DISTINCT parameters FROM keywords WHERE power_keyword = :keyword AND command = :command"), {"keyword": f'{power_keyword}', "command": f'{command}'})
    parameters = result.fetchall()
    cur.close()
    return [parameter[0] for parameter in parameters]

async def suggest_query(power_keyword: str, command: str, parameter: str):
    """Fetches and returns complete queries for given parameters."""
    cur = question_engine.connect()
    result = cur.execute(text("SELECT query FROM keywords WHERE power_keyword = :keyword AND command = :command AND parameters = :parameter"), {"keyword": f'{power_keyword}', "command": f'{command}', "parameter": f'{parameter}'})
    queries = result.fetchall()
    cur.close()
    return [query[0] for query in queries]

def get_json_data(df, sql_query):
    """Convert the result of a SQL query into a JSON-compatible structure."""
    columns = df.columns.tolist()
    rows = df.to_dict(orient='records')
    json_structure = {
        "query": sql_query,
        "columns": columns,
        "rows": rows
    }
    return json_structure
        
def execute_query(sql_query, database_name):
    """Execute a SQL query and return the result."""
    engine = qa_service.postgres_connection_string(database_name)
    success, error_message = qa_service.validate_query(engine, sql_query)
    print(error_message)
    if not success:
        corrected_sql_query = qa_service.llm_validation(sql_query, error_message)
    else:
        corrected_sql_query = sql_query
    print(corrected_sql_query)
    
    try:    
        result = pd.read_sql(text(corrected_sql_query), engine)
        return corrected_sql_query, result
    except Exception as e:
        print(f"Error executing query: {e}")
        return None


def table_descriptions(entities):
    """Get descriptions for a list of entities."""
    table_descriptions = []
    print(entities)
    for entity in entities:
        table_description = qa_service.get_table_description(entity)
        table_descriptions.append(table_description.description)
    return table_descriptions

def get_schema(table_descriptions):
    """Get schema information based on table descriptions."""
    semantic_search_results = []
    print(table_descriptions)
    for description in table_descriptions:
        result = qa_service.get_similar_questions(description)
        print(result)
        semantic_search_results.append(result)
    schema_text = []
    for result in semantic_search_results:
        schema_text.append(result['matches'][0]['metadata'])
    print(schema_text)
    return schema_text

def display_answer(result):
    """Display the answer from the result."""
    return result['metadata']['query']

def handle_low_score(user_input):
    """Handle cases where the query score is low."""
    entities = qa_service.get_entities(user_input).entities
    entities_list = [entity.entity for entity in entities]
    table_descriptions_list = table_descriptions(entities_list)
    schema = get_schema(table_descriptions_list)
    sql_query = qa_service.get_query(user_input, schema)
    return sql_query

def handle_results(results, user_input):
    """Handle the results of a query."""
    best_match = results['matches'][0]
    if (best_match['score'] > 0.7):
        sql_query = display_answer(best_match)
        return sql_query
    else:
        return handle_low_score(user_input)

@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "API is running"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8002, reload=True)
