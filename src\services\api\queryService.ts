
import { QueryResultData } from '@/components/dashboard/models';

const API_BASE_URL = import.meta.env.VITE_DADA_API_URL;

const exportToCSV = async (prompt: string): Promise<string> => {
  try {
    console.log(`Sending export request to ${API_BASE_URL}/api/export with prompt: ${prompt}`);

    const response = await fetch(`${API_BASE_URL}/api/export?nl_question=${encodeURIComponent(prompt)}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "text/csv",
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(errorData || `Server responded with status ${response.status}`);
    }

    const blob = await response.blob();
    return handleCSVDownload(blob, response.headers);
  } catch (error) {
    console.error("Export API Error:", error instanceof Error ? error.message : error);
    throw error;
  }
};

const regenerateQuery = async (prompt: string): Promise<QueryResultData> => {
  try {
    console.log(`Sending regenerate request to ${API_BASE_URL}/api/regenerate with prompt: ${prompt}`);

    const response = await fetch(`${API_BASE_URL}/api/regenerate?nl_question=${encodeURIComponent(prompt)}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.detail || `Server responded with status ${response.status}`);
    }

    const data = await response.json();
    console.log("Received API regenerate response:", data);

    return {
      query: data.query,
      tableData: {
        columns: data.columns,
        rows: data.rows,
      },
    };
  } catch (error) {
    console.error("Regenerate API Error:", error instanceof Error ? error.message : error);
    throw error;
  }
};

// Helper function to handle CSV file download
const handleCSVDownload = (blob: Blob, headers: Headers): string => {
  const contentDisposition = headers.get('Content-Disposition');
  let filename = 'query_result.csv';
  
  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
    if (filenameMatch && filenameMatch[1]) {
      filename = filenameMatch[1].replace(/['"]/g, '');
    }
  }
  
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  window.URL.revokeObjectURL(url);
  
  return filename;
};

// Export as a default object
const queryService = {
  exportToCSV,
  regenerateQuery
};

export default queryService;
