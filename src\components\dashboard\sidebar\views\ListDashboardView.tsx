
import React from 'react';
import { PanelLeftClose } from "lucide-react";
import SearchInput from '../search/SearchInput';
import ChatTopicsList from '../ChatTopicsList';
import ProjectsList from '../ProjectsList';
import MeetingsList from '../MeetingsList';
import NewItemButton from '../NewItemButton';
import { ChatTopic, Project, Meeting } from '../types';

interface ListDashboardViewProps {
  searchQuery: string;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  filteredItems: ChatTopic[] | Project[] | Meeting[];
  toggleItem: (id: string) => void;
  addChatToItem: (id: string) => void;
  addNewItem: () => void;
  dashboardType: 1 | 2 | 3;
  onToggle: () => void;
}

const ListDashboardView: React.FC<ListDashboardViewProps> = ({
  searchQuery,
  handleSearch,
  filteredItems,
  toggleItem,
  addChatToItem,
  addNewItem,
  dashboardType,
  onToggle
}) => {
  const getSearchPlaceholder = () => {
    if (dashboardType === 1) return "Search Chat...";
    if (dashboardType === 2) return "Search Meeting...";
    return "Search Project...";
  };

  return (
    <div className="w-64 flex flex-col h-full bg-gray-200 border group">
      <div className="p-3">
        <div className="relative">
          <SearchInput
            searchQuery={searchQuery}
            handleSearch={handleSearch}
            placeholder={getSearchPlaceholder()}
          />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto py-2">
        <div className="space-y-4 p-1">
          <NewItemButton 
            label={
              dashboardType === 1 ? "New Chat" : 
              dashboardType === 2 ? "New Meeting" : 
              "New Project"
            }
            onClick={addNewItem} 
          />
          {dashboardType === 1 ? (
            <ChatTopicsList
              items={filteredItems as ChatTopic[]}
              toggleItem={toggleItem}
              addChatToItem={addChatToItem}
            />
          ) : dashboardType === 2 ? (
            <MeetingsList
              items={filteredItems as Meeting[]}
              toggleItem={toggleItem}
            />
          ) : (
            <ProjectsList
              items={filteredItems as Project[]}
              toggleItem={toggleItem}
              addChatToItem={addChatToItem}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ListDashboardView;
