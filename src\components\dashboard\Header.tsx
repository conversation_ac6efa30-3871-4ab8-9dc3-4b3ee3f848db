
import React, { useState } from 'react';
import { Menu, PanelLeftClose, PanelLeftOpen, Search, Bell, User, LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Separator } from '@/components/ui/separator';
import { Link, useNavigate } from 'react-router-dom';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
} from "@/components/ui/dropdown-menu";

type DashboardType = 1 | 2 | 3;

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarCollapsed: boolean;
  rightSidebarVisible: boolean;
  dashboardName: string;
  toggleRightSidebar?: () => void;
  onHeaderSearch?: (query: string) => void;
  dashboardType: DashboardType;
}

const Header = ({ 
  toggleSidebar, 
  sidebarCollapsed, 
  dashboardName, 
  rightSidebarVisible, 
  toggleRightSidebar,
  onHeaderSearch,
  dashboardType
}: HeaderProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const getDashboardTitle = () => {
    switch (dashboardName) {
      case 'Dashboard-1':
        return 'Chatbot';
      case 'Dashboard-2':
        return ' Transcript AI';
      case 'Dashboard-3':
        return 'DADA AI';
      default:
        return dashboardName;
    }
  };

  const getDahboardSearchTitle = () => {
    switch (dashboardName) {
      case 'Dashboard-1':
        return 'Search Chat...';
      case 'Dashboard-2':
        return 'Search Transcript...';
      case 'Dashboard-3':
        return 'Search Project...';
      default:
        return 'Search...';
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    if (onHeaderSearch) {
      onHeaderSearch(value);
    }
  };

  const handleTitleClick = () => {
    navigate('/');
  };

  const getSidebarButtonClass = (isCollapsed: boolean) => `
    p-2 rounded-md hover:bg-gray-100 transition-colors
    ${isCollapsed ? 'text-gray-500' : 'text-black'}
  `;

  const renderMenuButton = () => {
    if (dashboardType === 2) {
      return (
        <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
          <DropdownMenuTrigger asChild>
            <button 
              className="p-2 rounded-md hover:bg-gray-100 transition-colors"
              onMouseEnter={() => setIsDropdownOpen(true)}
              onMouseLeave={() => setIsDropdownOpen(false)}
            >
              <Menu size={20} />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent 
            align="start" 
            className="w-56"
            onMouseEnter={() => setIsDropdownOpen(true)}
            onMouseLeave={() => setIsDropdownOpen(false)}
          >
            <DropdownMenuItem>
              New Transcript
            </DropdownMenuItem>
            <DropdownMenuItem>
              Settings
            </DropdownMenuItem>
            {/* <DropdownMenuSeparator /> */}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                Admin
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                <DropdownMenuItem onClick={() => navigate('/Admin/menu')}>
                  Admin Menu
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuSub>
            <DropdownMenuItem>
              SignOut
            </DropdownMenuItem>
            {/* <DropdownMenuSeparator /> */}
           
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }

    return (
      <button 
        onClick={toggleSidebar} 
        className={`p-2 rounded-md hover:bg-gray-100 transition-colors ${
          sidebarCollapsed ? 'text-gray-500' : 'text-black'
        }`}
        aria-label={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
      >
        <Menu size={20} />
      </button>
    );
  };

  return (
    <header className="bg-gray-200 border-b border-gray-200 z-10 sticky top-0 text-black">
      <div className="flex items-center justify-between px-4 py-2">
        {/* Left side - Menu, Logo and Title */}
        <div className="flex items-center gap-2">
          {renderMenuButton()}
          <img 
            src="/Mindlabz-logo.svg" 
            alt="MindLabz Logo" 
            className="h-8 w-auto cursor-pointer"
            onClick={handleTitleClick}
          />
          <Separator orientation="vertical" size='5' className=" bg-green-500 ml-2" />
          <div 
            className="font-medium pl-2 text-large font-serif cursor-pointer hover:text-blue-500 transition-colors"
            onClick={handleTitleClick}
          >
            {getDashboardTitle()}
          </div>
        </div>
        
        {/* Center - Search input */}
        <div className="flex-1 max-w-lg mx-auto">
          <div className="relative text-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-black-500"/>
            </div>
            <input
              type="text"
              placeholder={getDahboardSearchTitle()}
              className="block w-full pl-10 pr-3 py-1  bg-white-500 text-black placeholder-black/70 rounded-md focus:outline-none focus:ring-1 focus:ring-gray-300"
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
        </div>
        
        {/* Right side - Bell and User Profile */}
        <div className="flex items-center space-x-6">
          <button 
            className="text-black-700 hover:text-blue-900"
            aria-label="Notifications"
          >
            <Bell size={20} />
          </button>

          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center space-x-2 hover:text-blue-500">
              <div className="w-8 h-8 rounded-full bg-black text-white flex items-center justify-center text-sm font-small">
                {user?.username ? 
                  (() => {
                    // Split the full name into parts
                    const nameParts = user.username.split(' ');
                    
                    // If there's only one part, use the first and last character
                    if (nameParts.length === 1) {
                      return `${nameParts[0].charAt(0)}${nameParts[0].charAt(nameParts[0].length - 1)}`.toUpperCase();
                    }
                    
                    // If there are multiple parts, use first and last part only
                    const firstName = nameParts[0];
                    const lastName = nameParts[nameParts.length - 1];
                    
                    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
                  })() : 
                  'U'
                }
              </div>
              {/* <span className="text-sm font-medium">{user?.username || 'User'}</span> */}
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem onClick={logout}>
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {toggleRightSidebar && (
            <button 
              onClick={toggleRightSidebar}
              className={getSidebarButtonClass(!rightSidebarVisible)}
              aria-label={rightSidebarVisible ? "Collapse right sidebar" : "Expand right sidebar"}
            >
              {rightSidebarVisible ? <PanelLeftOpen size={20} /> : <PanelLeftClose size={20} />}
            </button>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
