import React, { memo, useMemo, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, MessageSquare, History, X } from 'lucide-react';
import { TrackerItem } from '@/hooks/useTrackerItems';
import { DataTable } from '@/components/ui/data-table/data-table';
import { ColumnHeader } from '@/components/ui/data-table/column-header';
import { ColumnFilter } from '@/components/ui/data-table/column-filter';
import { ColumnDef } from '@tanstack/react-table';
import { Input } from '@/components/ui/input';
import TrackerStatusCell from './TrackerStatusCell';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { ChangeHistoryDialog } from './ChangeHistoryDialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useResponsiveColumns } from '@/hooks/useResponsiveColumns';
import ColumnVisibilityMenu from './ColumnVisibilityMenu';

interface TrackerTableProps {
  trackerItems: TrackerItem[];
  expandedRows: Record<string, boolean>;
  handleInputChange: (id: string, field: string, value: any) => void;
  handleStatusChange: (id: string, status: string) => void;
  toggleRowExpansion: (id: string) => void;
  setActiveCommentId: (id: string | null) => void;
  onAddNewRow: () => string; // Updated to return the new row ID
  onClearRow: (rowId: string) => void; // New prop for clearing a row
}

const TrackerTable: React.FC<TrackerTableProps> = ({
  trackerItems,
  expandedRows,
  handleInputChange,
  handleStatusChange,
  toggleRowExpansion,
  setActiveCommentId,
  onAddNewRow,
  onClearRow
}) => {
  const [hasNewRow, setHasNewRow] = useState(false);
  const [newRowId, setNewRowId] = useState<string | null>(null);

  const handleAddRow = () => {
    const newId = onAddNewRow(); // Assuming onAddNewRow returns the new row ID
    setHasNewRow(true);
    setNewRowId(newId);
  };

  const handleClearRow = () => {
    if (newRowId) {
      onClearRow(newRowId);
      setHasNewRow(false);
      setNewRowId(null);
    }
  };

  const allColumns = useMemo<ColumnDef<TrackerItem>[]>(() => [
    {
      accessorKey: 'projectName',
      header: ({ column }) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <ColumnHeader column={column} title="Project Name" />
            <ColumnFilter column={column} title="Project Name" />
          </div>
        </div>
      ),
      cell: ({ row }) => <div className="truncate">{row.original.projectName}</div>,
      enableSorting: true,
      enableColumnFilter: true,
      size: 150, // Set explicit width
    },
    {
      accessorKey: 'activity',
      header: ({ column }) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <ColumnHeader column={column} title="Activity" />
            <ColumnFilter column={column} title="Activity" />
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const rowId = row.original.id;
        const isExpanded = expandedRows[rowId] || false;
        
        return (
          <div 
            className="w-full min-w-[250px]"
            onDoubleClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              toggleRowExpansion(rowId);
            }}
          >
            <Input 
              value={typeof row.original.activity === 'string' ? row.original.activity : 
                    (row.original.activity ? JSON.stringify(row.original.activity) : '')}
              onChange={(e) => handleInputChange(rowId, 'activity', e.target.value)}
              className="h-8 text-xs px-2 w-full border-0 shadow-none bg-transparent focus-visible:ring-1 focus-visible:ring-teal-500"
              style={{ 
                backgroundColor: isExpanded ? '#EFF6FF' : 'transparent',
                width: '100%'
              }}
            />
          </div>
        );
      },
      enableSorting: true,
      enableColumnFilter: true,
      size: 300, // Set explicit width for activity column
    },
    {
      accessorKey: 'activityType',
      header: ({ column }) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <ColumnHeader column={column} title="Activity Type" />
            <ColumnFilter column={column} title="Activity Type" />
          </div>
        </div>
      ),
      cell: ({ row }) => <div className="min-w-[120px]">{row.original.activityType}</div>,
      enableSorting: true,
      enableColumnFilter: true,
      size: 120, // Set explicit width
    },
    {
      accessorKey: 'assignedOn',
      header: ({ column }) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <ColumnHeader column={column} title="Assigned On" />
            <ColumnFilter column={column} title="Assigned On" />
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const formattedDate = row.original.assignedOn 
          ? format(new Date(row.original.assignedOn), 'yyyy-MM-dd')
          : '';
        
        return <div className="text-center min-w-[120px]">{formattedDate}</div>;
      },
      enableSorting: true,
      enableColumnFilter: true,
      size: 120, // Set explicit width
    },
    {
      accessorKey: 'activityStatus',
      header: ({ column }) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <ColumnHeader column={column} title="Status" />
            <ColumnFilter column={column} title="Status" />
          </div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="min-w-[120px]">
          <TrackerStatusCell 
            status={row.original.activityStatus} 
            onStatusChange={(value) => handleStatusChange(row.original.id, value)} 
          />
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
      size: 120, // Set explicit width
    },
    {
      accessorKey: 'assignedTo',
      header: ({ column }) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <ColumnHeader column={column} title="Assigned To" />
            <ColumnFilter column={column} title="Assigned To" />
          </div>
        </div>
      ),
      cell: ({ row }) => {
        const rowId = row.original.id;
        
        const teamMembers = row.original.availableTeamMembers && row.original.availableTeamMembers.length > 0 
          ? row.original.availableTeamMembers[0].split(',').map(email => email.trim())
          : [];
        
        return (
          <div className="min-w-[150px]">
            <Select
              value={row.original.assignedTo || ''}
              onValueChange={(value) => handleInputChange(rowId, 'assignedTo', value)}
            >
              <SelectTrigger className="h-8 text-xs px-2 border-0 shadow-none bg-transparent focus:ring-1 focus:ring-teal-500">
                <SelectValue placeholder="Assign to..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Unassigned</SelectItem>
                {teamMembers.map((email, index) => (
                  <SelectItem key={index} value={email}>{email}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );
      },
      enableSorting: true,
      enableColumnFilter: true,
      size: 150, // Set explicit width
    },
    {
      accessorKey: 'eta',
      header: ({ column }) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <ColumnHeader column={column} title="ETA" />
            <ColumnFilter column={column} title="ETA" />
          </div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="min-w-[120px]">
          <Popover>
            <PopoverTrigger asChild>
              <div className="flex items-center w-full cursor-pointer relative">
                <Input 
                  value={row.original.eta ? format(new Date(row.original.eta), 'dd-MM-yyyy') : ''} 
                  readOnly
                  className="h-8 text-xs px-2 border-0 shadow-none bg-transparent cursor-pointer"
                />
                {!row.original.eta && <CalendarIcon className="h-4 w-4 absolute right-2 text-gray-400" />}
              </div>
            </PopoverTrigger>
            <PopoverContent className="p-0" align="start">
              <Calendar
                mode="single"
                selected={row.original.eta ? new Date(row.original.eta) : undefined}
                onSelect={(date) => {
                  if (date) {
                    handleInputChange(
                      row.original.id, 
                      'eta', 
                      date.toISOString()
                    );
                  }
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
      size: 120, // Set explicit width
    },
    {
      accessorKey: 'meetingId',
      header: ({ column }) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <ColumnHeader column={column} title="Meeting ID" />
            <ColumnFilter column={column} title="Meeting ID" />
          </div>
        </div>
      ),
      cell: ({ row }) => <div className="text-center min-w-[100px]">{row.original.meetingId}</div>,
      enableSorting: true,
      enableColumnFilter: true,
      size: 100, // Set explicit width
    },
    {
      accessorKey: 'comments',
      header: ({ column }) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <ColumnHeader column={column} title="Comments" />
            <ColumnFilter column={column} title="Comments" />
          </div>
        </div>
      ),
      cell: ({ row }) => (
        <div 
          className="relative flex items-center h-full w-full cursor-pointer min-w-[200px]"
          onClick={() => setActiveCommentId(row.original.id)}
        >
          <div className="flex-1 truncate text-xs">
            {row.original.comments ? row.original.comments.substring(0, 30) + (row.original.comments.length > 30 ? '...' : '') : 'Click to add comment'}
          </div>
          <div className="text-blue-500 ml-1">
            <MessageSquare size={14} />
          </div>
        </div>
      ),
      enableSorting: true,
      enableColumnFilter: true,
      size: 200, // Set explicit width
    },
    {
      accessorKey: 'tags',
      header: ({ column }) => (
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <ColumnHeader column={column} title="Tags" />
            <ColumnFilter column={column} title="Tags" />
          </div>
        </div>
      ),
      cell: ({ row }) => <div className="min-w-[100px]">{row.original.tags || ''}</div>,
      enableSorting: true,
      enableColumnFilter: true,
      size: 100, // Set explicit width
    },
    {
      id: 'history',
      header: () => <div className="text-center">History</div>,
      cell: ({ row }) => {
        const [isOpen, setIsOpen] = useState(false);
        
        return (
          <div className="flex justify-center items-center">
            <button 
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              title="View change history"
              onClick={() => setIsOpen(true)}
            >
              <History size={16} />
            </button>
            
            <ChangeHistoryDialog 
              isOpen={isOpen}
              onClose={() => setIsOpen(false)}
              trackerId={row.original.id}
              activityName={row.original.activity}
            />
          </div>
        );
      },
      size: 80, // Set explicit width
    },
  ], [expandedRows, handleInputChange, handleStatusChange, setActiveCommentId, toggleRowExpansion]);
  
  const { 
    visibleColumns, 
    columnVisibility, 
    toggleColumnVisibility,
    showAllColumns,
    showEssentialOnly,
    resetToDefaults,
    windowWidth,
    visibleColumnCount,
    totalColumnCount
  } = useResponsiveColumns(allColumns);
  
  const tableMinWidth = useMemo(() => {
    const baseWidth = visibleColumnCount * 180;
    const minWidth = Math.max(baseWidth, 1000);
    
    if (windowWidth >= 1536) {
      return `${Math.min(minWidth, 1800)}px`;
    } else if (windowWidth >= 1280) {
      return `${Math.min(minWidth, 1600)}px`;
    } else if (windowWidth >= 1024) {
      return `${Math.min(minWidth, 1400)}px`;
    } else {
      return `${Math.min(minWidth, 800)}px`;
    }
  }, [visibleColumnCount, windowWidth]);

  return (
    <div className="flex flex-col h-full tracker-table-container">
      <div className="flex justify-between items-center p-3 bg-gray-50 border-b">
        <div className="flex items-center gap-2">
          <ColumnVisibilityMenu 
            columns={allColumns}
            columnVisibility={columnVisibility}
            toggleColumnVisibility={toggleColumnVisibility}
            showAllColumns={showAllColumns}
            showEssentialOnly={showEssentialOnly}
            resetToDefaults={resetToDefaults}
            visibleColumnCount={visibleColumnCount}
            totalColumnCount={totalColumnCount}
          />
          
          {hasNewRow && (
            <Button 
              onClick={handleClearRow}
              className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 text-sm flex items-center"
              size="sm"
            >
              <X className="h-4 w-4 mr-1" /> Clear New Row
            </Button>
          )}
        </div>
        
        <Button 
          onClick={handleAddRow}
          className="bg-teal-600 hover:bg-teal-700 text-white px-3 py-1 text-sm flex items-center"
          size="sm"
        >
          <Plus className="h-4 w-4 mr-1" /> Add Row
        </Button>
      </div>
      
      {/* This div is critical for proper scrolling */}
      <div 
        style={{ 
          height: 'calc(100% - 56px)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <DataTable 
          columns={visibleColumns} 
          data={trackerItems} 
          minWidth="1500px"
          className="tracker-table-scroll"
        />
      </div>
    </div>
  );
};

export default memo(TrackerTable);
