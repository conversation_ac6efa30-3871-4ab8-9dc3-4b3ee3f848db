
import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/hooks/useRedux';
import { Button } from "@/components/ui/button";
import { Folder, FolderOpen, Loader2, Plus } from "lucide-react";
import { ChevronDown, ChevronUp } from "lucide-react";
import CreateFolderDialog from './CreateFolderDialog';
import FolderItem from './file/FolderItem';
import { FolderType } from '@/services/api/audioTranscript/types';
import VirtualizedFileList from './file/VirtualizedFileList';

interface FolderItem {
  name: string;
  type: 'folder';
  files: string[];
  subFolders?: FolderItem[];
}

interface FileSectionProps {
  title: string;
  folderType: FolderType;
  files: string[];
  isExpanded: boolean;
  toggleExpanded: () => void;
  onFileClick: (fileName: string, folderType: FolderType) => void;
  onDeleteClick: (fileName: string, folderType: FolderType) => void;
  isLoading?: boolean;
  iconColor?: string;
  isTracker?: boolean;
  trackerProjects?: Array<{id: string; name: string; fileCount?: number}>;
}

const ITEMS_PER_PAGE = 5;

const FileSection: React.FC<FileSectionProps> = ({
  title,
  folderType,
  files,
  isExpanded,
  toggleExpanded,
  onFileClick,
  onDeleteClick,
  isLoading,
  iconColor,
  isTracker = false,
  trackerProjects = []
}) => {
  const navigate = useNavigate();
  
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const [folders, setFolders] = useState<FolderItem[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<string[]>([]);
  const [currentParentFolder, setCurrentParentFolder] = useState<string | null>(null);
  const [displayedFiles, setDisplayedFiles] = useState<string[]>(files.slice(0, ITEMS_PER_PAGE));
  const [currentPage, setCurrentPage] = useState(1);
  
  // Add state for tracker projects pagination
  const [displayedProjects, setDisplayedProjects] = useState<typeof trackerProjects>(
    trackerProjects.slice(0, ITEMS_PER_PAGE)
  );

  // Update displayed files when files prop changes
  useEffect(() => {
    setDisplayedFiles(files.slice(0, currentPage * ITEMS_PER_PAGE));
  }, [files, currentPage]);
  
  // Update displayed projects when trackerProjects prop changes
  useEffect(() => {
    if (isTracker) {
      // Only update if the component is mounted and trackerProjects has changed
      setDisplayedProjects(trackerProjects.slice(0, currentPage * ITEMS_PER_PAGE));
    }
  }, [trackerProjects, currentPage, isTracker]);

  // Make sure loadMoreItems is properly memoized with all dependencies
  const loadMoreItems = useCallback(() => {
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);
    
    if (isTracker) {
      setDisplayedProjects(trackerProjects.slice(0, nextPage * ITEMS_PER_PAGE));
    } else {
      setDisplayedFiles(files.slice(0, nextPage * ITEMS_PER_PAGE));
    }
  }, [currentPage, files, trackerProjects, isTracker]);

  const handleCreateFolder = useCallback((folderName: string, parentFolder?: string) => {
    if (parentFolder) {
      handleCreateSubFolder(parentFolder, folderName);
      setExpandedFolders(prev => 
        prev.includes(parentFolder) ? prev : [...prev, parentFolder]
      );
    } else {
      const newFolder: FolderItem = {
        name: folderName,
        type: 'folder',
        files: [],
        subFolders: []
      };
      setFolders(folders => [...folders, newFolder]);
    }
    setCurrentParentFolder(null);
  }, []);

  const toggleFolder = useCallback((folderName: string) => {
    setExpandedFolders(prev => 
      prev.includes(folderName)
        ? prev.filter(name => name !== folderName)
        : [...prev, folderName]
    );
  }, []);

  const handleDeleteFolder = useCallback((folderName: string) => {
    setFolders(prev => prev.filter(folder => folder.name !== folderName));
  }, []);

  const handleCreateSubFolder = useCallback((parentFolderName: string, subFolderName: string) => {
    setFolders(prev => prev.map(folder => {
      if (folder.name === parentFolderName) {
        return {
          ...folder,
          subFolders: [...(folder.subFolders || []), {
            name: subFolderName,
            type: 'folder',
            files: [],
          }]
        };
      }
      return folder;
    }));
  }, []);

  const handleFileClick = useCallback((fileName: string, folderType: FolderType) => {
    if (folderType === 'tracker') {
      // For tracker files, navigate to tracker view with project name
      const project = trackerProjects.find(p => p.id === fileName || p.name === fileName);
      if (project) {
        navigate(`/transcript/tracker/${encodeURIComponent(project.name)}`);
      }
    } else {
      // For regular files, use the provided onFileClick handler
      onFileClick(fileName, folderType);
    }
  }, [trackerProjects, navigate, onFileClick]);

  return (
    <div className="mb-2">
      <div className="flex items-center justify-between w-full">
        <Button
          variant="ghost"
          className="w-full justify-between text-sm py-2 mb-1 font-medium"
          onClick={toggleExpanded}
        >
          <div className="flex items-center">
            {isExpanded ? (
              <FolderOpen className={`h-5 w-5 mr-2 ${iconColor}`} />
            ) : (
              <Folder className={`h-5 w-5 mr-2 ${iconColor}`} />
            )}
            {title}
            {isLoading && (
              <Loader2 className="h-4 w-4 ml-2 animate-spin text-blue-800"/>
            )}
          </div>
          <div className="flex items-center gap-2">
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </div>
        </Button>
        {!isTracker && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 hover:bg-gray-100 ml-2"
            onClick={() => setIsCreateFolderOpen(true)}
          >
            <Plus className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
          </Button>
        )}
      </div>

      {isExpanded && (
        <>
          <div className="ml-6 space-y-0">
            {folders.map((folder) => (
              <FolderItem
                key={folder.name}
                folder={folder}
                iconColor={iconColor}
                isExpanded={expandedFolders.includes(folder.name)}
                onToggle={toggleFolder}
                onCreateSubFolder={(parentFolder) => {
                  setCurrentParentFolder(parentFolder);
                  setIsCreateFolderOpen(true);
                }}
                onDeleteFolder={handleDeleteFolder}
                onDeleteSubFolder={(parentFolder, subFolderName) => {
                  setFolders(prev => prev.map(f => {
                    if (f.name === parentFolder) {
                      return {
                        ...f,
                        subFolders: f.subFolders?.filter(sf => sf.name !== subFolderName)
                      };
                    }
                    return f;
                  }));
                }}
              />
            ))}

            {isTracker ? (
              // Display projects for tracker
              <div className="space-y-1">
                {displayedProjects.length > 0 ? (
                  displayedProjects.map(project => (
                    <div 
                      key={project.id}
                      className="flex items-center py-1 px-2 hover:bg-gray-100 rounded cursor-pointer"
                      onClick={() => handleFileClick(project.id, 'tracker')}
                    >
                      <span className="text-sm text-gray-700">{project.name}</span>
                      {project.fileCount !== undefined && project.fileCount > 0 && (
                        <span className="text-xs bg-gray-200 rounded-full px-2 ml-2">
                          {project.fileCount}
                        </span>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-400 py-1">
                    No tracker projects yet
                  </div>
                )}
              </div>
            ) : (
              // Display regular files
              <VirtualizedFileList
                files={displayedFiles}
                folderType={folderType}
                onFileClick={handleFileClick}
                onDeleteClick={onDeleteClick}
              />
            )}

            {!isTracker && files.length === 0 && folders.length === 0 && !isLoading && (
              <div className="text-sm text-gray-400 py-1">
                No files yet
              </div>
            )}
          </div>

          {/* Load More button for both regular files and tracker projects */}
          {!isLoading && (
            (isTracker && trackerProjects.length > displayedProjects.length) || 
            (!isTracker && files.length > displayedFiles.length)
          ) && (
            <Button
              variant="ghost"
              size="sm"
              className="w-full text-sm text-gray-500 hover:text-gray-700 mt-1"
              onClick={loadMoreItems}
            >
              Load More {isTracker ? 'Projects' : 'Files'}
            </Button>
          )}
        </>
      )}

      {isCreateFolderOpen && (
        <CreateFolderDialog
          isOpen={isCreateFolderOpen}
          onClose={() => {
            setIsCreateFolderOpen(false);
            setCurrentParentFolder(null);
          }}
          onSave={handleCreateFolder}
          folderType={folderType}
          parentFolder={currentParentFolder || undefined}
        />
      )}
    </div>
  );
};

export default React.memo(FileSection);
