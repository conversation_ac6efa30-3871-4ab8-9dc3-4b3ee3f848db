
import { render, screen } from '@testing-library/react';
import { ChartTitle } from '../ChartTitle';
import { describe, it, expect, vi } from 'vitest';
import * as ChartUtils from '../ChartUtils';

// Mock getChartTitle function
vi.mock('../ChartUtils', () => ({
  getChartTitle: vi.fn().mockImplementation((query, categoryAxis, dataColumns) => {
    return `Chart of ${categoryAxis} vs ${dataColumns.join(', ')}`;
  })
}));

describe('ChartTitle', () => {
  it('renders with the correct title', () => {
    const props = {
      query: 'SELECT * FROM data',
      categoryAxis: 'category',
      dataColumns: ['value1', 'value2']
    };
    
    render(<ChartTitle {...props} />);
    
    expect(screen.getByText('Chart of category vs value1, value2')).toBeInTheDocument();
  });
  
  it('handles missing query', () => {
    const props = {
      categoryAxis: 'category',
      dataColumns: ['value']
    };
    
    render(<ChartTitle {...props} />);
    
    expect(screen.getByText('Chart of category vs value')).toBeInTheDocument();
  });
});
