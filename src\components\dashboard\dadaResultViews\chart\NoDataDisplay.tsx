
import React from "react";

interface NoDataDisplayProps {
  height: string;
  textSize: string;
  message?: string;
}

export const NoDataDisplay: React.FC<NoDataDisplayProps> = ({
  height,
  textSize,
  message = "No data available for chart visualization"
}) => (
  <div className={`${height} bg-gray-50 rounded border border-gray-200 flex items-center justify-center`}>
    <p className={`text-gray-500 ${textSize}`}>{message}</p>
  </div>
);
