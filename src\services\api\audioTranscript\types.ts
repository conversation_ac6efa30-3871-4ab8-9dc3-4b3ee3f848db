
import { TranscriptItem } from '@/components/dashboard/fileUpload/types';

export interface SummaryData {
  summary: string;
  actionItems: string[];
  importantPoints: string[];
  openQuestions: string[];
}

export interface TranscriptResponse {
  message: string;
  transcript: string | TranscriptItem[];
  summary: SummaryData;
  file_id: number;
  audioUrl: string;
  audio_url?: string; // Add this for backward compatibility with API responses
  email?: string;
  username?: string;
}

export interface FileListResponse {
  status: string;
  files: string[];
  count: number;
}

export type FolderType = 'personal' | 'project' | 'tracker';
