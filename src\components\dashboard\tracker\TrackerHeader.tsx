
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { TranscriptOption } from '@/hooks/useTrackerItems';
import { Share2, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Autocomplete } from '@/components/ui/autocomplete';

interface TrackerHeaderProps {
  projectName: string;
  transcriptOptions: TranscriptOption[];
  onTranscriptSelect?: (value: string | string[]) => void;
  onShowAssignedToMeChange?: (checked: boolean) => void;
  onShare?: () => void;
}

const TrackerHeader: React.FC<TrackerHeaderProps> = ({ 
  projectName, 
  transcriptOptions,
  onTranscriptSelect,
  onShowAssignedToMeChange,
  onShare
}) => {
  const [showAssignedToMe, setShowAssignedToMe] = useState(false);
  const [selectedTranscripts, setSelectedTranscripts] = useState<string[]>([]);

  const handleTranscriptChange = (value: string | string[]) => {
    const transcriptValues = Array.isArray(value) ? value : [value];
    setSelectedTranscripts(transcriptValues);
    if (onTranscriptSelect) {
      onTranscriptSelect(transcriptValues);
    }
  };

  const clearTranscripts = () => {
    setSelectedTranscripts([]);
    if (onTranscriptSelect) {
      onTranscriptSelect([]);
    }
  };

  const handleShowAssignedToMeChange = (checked: boolean) => {
    setShowAssignedToMe(checked);
    if (onShowAssignedToMeChange) {
      onShowAssignedToMeChange(checked);
    }
  };

  // Convert transcript options to the format expected by Autocomplete
  const formattedTranscriptOptions = transcriptOptions.map(option => ({
    value: option.value,
    label: option.label
  }));

  return (
    <div className="pb-4">
      <h2 className="text-xl font-semibold p-2 text-center">Project Tracker</h2>
      
      <Card className="p-4 mb-4 bg-gray-50">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="project-name" className="block mb-2 text-sm font-medium text-gray-700">
              Project:
            </Label>
            <div
              id="project-name"
              className="w-full bg-white border border-gray-300 rounded-md p-2 text-sm"
            >
              {projectName}
            </div>
          </div>
          
          <div>
            <Label htmlFor="transcript-select" className="block mb-2 text-sm font-medium text-gray-700">
              Transcript(s):
            </Label>
            <div className="relative">
              <Autocomplete
                options={formattedTranscriptOptions}
                value={selectedTranscripts}
                onValueChange={handleTranscriptChange}
                placeholder="Select transcripts"
                emptyMessage="No transcripts found"
                multiple={true}
                searchPlaceholder="Search transcripts..."
                contentClassName="mt-0"
                triggerClassName="rounded-t-md"
              />
              {selectedTranscripts.length > 0 && (
                <button
                  type="button"
                  onClick={clearTranscripts}
                  className="absolute right-10 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-gray-200"
                  aria-label="Clear selections"
                >
                  <X className="h-4 w-4 text-gray-500" />
                </button>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex justify-between items-center mt-4">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="assigned-to-me" 
              checked={showAssignedToMe}
              onCheckedChange={handleShowAssignedToMeChange}
            />
            <Label htmlFor="assigned-to-me" className="text-sm cursor-pointer">
              Show Assigned to me
            </Label>
          </div>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center"
            onClick={onShare}
          >
            <Share2 className="h-4 w-4 mr-1" />
            Share
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default TrackerHeader;
