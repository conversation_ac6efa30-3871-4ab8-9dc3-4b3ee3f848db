import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import fs from "fs";
import https from "https";

// Check if SSL certificate files exist
const sslCertExists = fs.existsSync('./cert.pem') && fs.existsSync('./key.pem');

// Create an HTTPS agent that ignores certificate validation errors
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    // Only use HTTPS if certificate files exist
    ...(sslCertExists ? {
      https: {
        key: fs.readFileSync('./key.pem'),
        cert: fs.readFileSync('./cert.pem'),
      }
    } : {}),
    proxy: {
      // Proxy API requests to your dashboard-2 backend
      '/api': {
        target: 'https://**********:8000',
        changeOrigin: true,
        secure: false, // This is important - it tells the proxy to ignore certificate validation
        agent: httpsAgent,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      // Proxy API requests to your dashboard-3 (DADA) backend
      '/dada-api': {
        target: 'https://***********:8002',
        changeOrigin: true,
        secure: false,
        agent: httpsAgent,
        rewrite: (path) => path.replace(/^\/dada-api/, '')
      }
    }
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
