
import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { TranscriptTab } from '@/components/dashboard/models';
import { ExtendedUploadedFile } from '@/components/dashboard/modelsExtension';
import { uploadAudioForTranscript, getTranscriptByFileName } from '@/services/api/audioTranscript';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { 
  addToPersonal, 
  addToProject, 
  clearCurrentTranscript,
  setCurrentTranscript 
} from '@/stores/fileSlice';
import { UploadedFile } from '@/components/dashboard/models';
import { TranscriptItem } from '@/components/dashboard/fileUpload/types';
import { baseUrl } from '@/services/api/audioTranscript/config';
import { useAuth } from '@/contexts/AuthContext';

export const useTranscriptUpload = (onFileUpload: (file: UploadedFile) => void) => {
  const dispatch = useAppDispatch();
  const { folderType, fileName } = useParams<{ folderType?: string; fileName?: string }>();
  const [uploadedFiles, setUploadedFiles] = useState<ExtendedUploadedFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<ExtendedUploadedFile | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [showFileSelector, setShowFileSelector] = useState(false);
  const [activeTab, setActiveTab] = useState<TranscriptTab>('transcript');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [isViewingExisting, setIsViewingExisting] = useState(false);
  const [currentFolderType, setCurrentFolderType] = useState<'personal' | 'project'>(
    (folderType as 'personal' | 'project') || 'personal'
  );
  const navigate = useNavigate();
  const { user } = useAuth();

  const [transcriptData, setTranscriptData] = useState<{
    transcript: TranscriptItem[];
    file_id?: number;
    audioUrl?: string;
    summary: string;
    actionItems: { action: string; dueDate: string; responsible: string }[];
    importantPoints: string[];
    openQuestions: { question: string }[];
  }>({
    transcript: [],
    file_id: undefined,
    audioUrl: undefined,
    summary: '',
    actionItems: [],
    importantPoints: [],
    openQuestions: []
  });

  const currentTranscript = useAppSelector(state => state.file.currentTranscript);

  // Effect to handle changes to currentTranscript from Redux
  useEffect(() => {
    if (currentTranscript?.data) {
      setSelectedFile({
        name: currentTranscript.fileName || '',
        type: 'audio/wav',
        size: 0,
        section: ['transcript', 'summary', 'actionItems', 'openQuestions'],
        url: '',
        file_id: currentTranscript.data.file_id
      });

      // Fix type mismatches by ensuring proper structure
      setTranscriptData({
        transcript: Array.isArray(currentTranscript.data.transcript) 
          ? currentTranscript.data.transcript 
          : [],
        file_id: currentTranscript.data.file_id,
        summary: currentTranscript.data.summary?.summary || '',
        actionItems: Array.isArray(currentTranscript.data.summary?.actionItems) 
          ? currentTranscript.data.summary.actionItems.map(item => {
              if (typeof item === 'string') {
                return { action: item, dueDate: '', responsible: '' };
              }
              return item;
            })
          : [],
        importantPoints: Array.isArray(currentTranscript.data.summary?.importantPoints)
          ? currentTranscript.data.summary.importantPoints
          : [],
        openQuestions: Array.isArray(currentTranscript.data.summary?.openQuestions)
          ? currentTranscript.data.summary.openQuestions.map(q => {
              if (typeof q === 'string') {
                return { question: q };
              }
              return q;
            })
          : []
      });

      if (currentTranscript.folderType) {
        setCurrentFolderType(currentTranscript.folderType);
      }

      setIsSubmitted(true);
      setIsLoading(false);
    }
  }, [currentTranscript]);

  // Effect to handle route params for shared transcript access
  useEffect(() => {
    // Update currentFolderType if folderType param is provided
    if (folderType && (folderType === 'personal' || folderType === 'project')) {
      setCurrentFolderType(folderType);
    }
  }, [folderType]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      dispatch(clearCurrentTranscript());
    };
  }, [dispatch]);

  // File handling functions
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return; // Early return if no files

    const file = files[0];
    const newFile: ExtendedUploadedFile = {
      name: file.name,
      type: file.type,
      size: file.size,
      section: ['transcript', 'summary', 'actionItems', 'openQuestions'],
      url: URL.createObjectURL(file),
      file: file
    };
    
    setUploadedFiles(prevFiles => [...prevFiles, newFile]);
    setSelectedFile(newFile);
    setShowFileSelector(true);
    setTranscriptData({
      transcript: [],
      file_id: undefined,
      summary: '',
      actionItems: [],
      importantPoints: [],
      openQuestions: []
    });
    setIsSubmitted(false);
  };

  const selectFile = (file: ExtendedUploadedFile) => {
    setSelectedFile(file);
    setDropdownOpen(false);
  };

  const clearFileInput = () => {
    setSelectedFile(null);
    setShowFileSelector(false);
    setIsSubmitted(false);
    setUploadedFiles([]); // Clear the uploaded files array
    setTranscriptData({
      transcript: [],
      file_id: undefined,
      summary: '',
      actionItems: [],
      importantPoints: [],
      openQuestions: []
    });
    
    // Clear the current transcript from Redux store
    dispatch(clearCurrentTranscript());
  };

  const handleSubmit = async () => {
    if (!selectedFile?.file) {
      toast.error("Error", {
        description: "No file selected or file is invalid"
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // Pass the user email and username to the upload function
      const response = await uploadAudioForTranscript(
        selectedFile.file, 
        user?.email, 
        user?.username
      );
      
      if (response) {
        console.log("Upload response:", response);
        
        // Extract audio URL from response - use the standardized property name
        let audioUrl = response.audioUrl;
        
        // For backward compatibility, also check audio_url if audioUrl is not present
        if (!audioUrl && response.audio_url) {
          audioUrl = response.audio_url;
        }
        
        // Format the audio URL if needed
        if (audioUrl && baseUrl && audioUrl.startsWith('/')) {
          audioUrl = `${baseUrl}${audioUrl}`;
        }
        
        console.log("Extracted audio URL:", audioUrl);
        
        // Update the transcript data with the audio URL
        setTranscriptData({
          transcript: Array.isArray(response.transcript) ? response.transcript : [],
          file_id: response.file_id,
          audioUrl: audioUrl, // Now this property is defined in the state type
          summary: response.summary?.summary || '',
          actionItems: Array.isArray(response.summary?.actionItems) 
            ? response.summary.actionItems.map(item => {
                if (typeof item === 'string') {
                  return { action: item, dueDate: '', responsible: '' };
                }
                return item;
              })
            : [],
          importantPoints: Array.isArray(response.summary?.importantPoints)
            ? response.summary.importantPoints
            : [],
          openQuestions: Array.isArray(response.summary?.openQuestions)
            ? response.summary.openQuestions.map(q => {
                if (typeof q === 'string') {
                  return { question: q };
                }
                return q;
              })
            : []
        });
        
        // Update the selectedFile with the file_id
        setSelectedFile(prev => prev ? {
          ...prev,
          file_id: response.file_id
        } : null);
        
        setIsSubmitted(true);
        
        toast.success("Success", {
          description: response.message
        });
        
        // Create a serializable version of the file for the onFileUpload prop
        const serializableFile = {
          name: selectedFile.name,
          type: selectedFile.type,
          size: selectedFile.size,
          section: selectedFile.section,
          url: selectedFile.url || ''
        };
        
        onFileUpload(serializableFile);
      }
    } catch (error) {
      toast.error("Error", {
        description: "Failed to process audio file"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveClick = () => {
    setIsSaveDialogOpen(true);
  };

  const handleSaveToPersonal = (editedFileName: string) => {
    if (selectedFile) {
      // Only update the name in selectedFile state
      setSelectedFile(prev => prev ? {
        ...prev,
        name: editedFileName
      } : null);

      dispatch(addToPersonal(editedFileName));
      toast.success("Success", {
        description: `${editedFileName} has been saved to your Personal folder`
      });
      setIsSaveDialogOpen(false);
    }
  };

  const handleSaveToProject = (editedFileName: string) => {
    if (selectedFile) {
      // Only update the name in selectedFile state
      setSelectedFile(prev => prev ? {
        ...prev,
        name: editedFileName
      } : null);

      dispatch(addToProject(editedFileName));
      toast.success("Success", {
        description: `${editedFileName} has been saved to your Project folder`
      });
      setIsSaveDialogOpen(false);
    }
  };

  const handleFileRetrieval = async (fileName: string, folderType: 'personal' | 'project') => {
    setIsLoading(true);
    try {
      // Pass user email and username to the retrieval function
      const response = await getTranscriptByFileName(
        fileName, 
        folderType, 
        user?.email, 
        user?.username
      );
      
      if (response) {
        // Extract audio URL from response
        let audioUrl = response.audioUrl;
        
        // For backward compatibility, also check audio_url if audioUrl is not present
        if (!audioUrl && response.audio_url) {
          audioUrl = response.audio_url;
        }
        
        // Format the audio URL if needed
        if (audioUrl && baseUrl && audioUrl.startsWith('/')) {
          audioUrl = `${baseUrl}${audioUrl}`;
        }
        
        // Fix type mismatches by ensuring proper structure
        setTranscriptData({
          transcript: Array.isArray(response.transcript) 
            ? response.transcript 
            : [],
          file_id: response.file_id,
          audioUrl: audioUrl,
          summary: response.summary?.summary || '',
          actionItems: Array.isArray(response.summary?.actionItems) 
            ? response.summary.actionItems.map(item => {
                if (typeof item === 'string') {
                  return { action: item, dueDate: '', responsible: '' };
                }
                return item;
              })
            : [],
          importantPoints: Array.isArray(response.summary?.importantPoints)
            ? response.summary.importantPoints
            : [],
          openQuestions: Array.isArray(response.summary?.openQuestions)
            ? response.summary.openQuestions.map(q => {
                if (typeof q === 'string') {
                  return { question: q };
                }
                return q;
              })
            : []
        });
        setIsSubmitted(true);
        setIsViewingExisting(true);
      }
    } catch (error) {
      toast.error("Error", {
        description: "Failed to retrieve transcript"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Determine available tabs based on content
  const availableTabs = isSubmitted ? [
    'transcript',
    ...(transcriptData.summary ? ['summary'] : []),
    ...(transcriptData.importantPoints.length > 0 ? ['notes'] : []),
    ...(transcriptData.actionItems.length > 0 ? ['actionItems'] : []),
    ...(transcriptData.openQuestions.length > 0 ? ['openQuestions'] : [])
  ] as TranscriptTab[] : [];

  // Make clearFileInput available globally
  useEffect(() => {
    window.clearFileInput = clearFileInput;
    return () => {
      delete window.clearFileInput;
    };
  }, []);

  return {
    uploadedFiles,
    selectedFile,
    dropdownOpen,
    showFileSelector,
    activeTab,
    isSubmitted,
    isLoading,
    isSaveDialogOpen,
    isViewingExisting,
    currentFolderType,
    transcriptData,
    availableTabs,
    setDropdownOpen,
    setActiveTab,
    setIsSaveDialogOpen,
    handleFileChange,
    selectFile,
    clearFileInput,
    handleSubmit,
    handleSaveClick,
    handleSaveToPersonal,
    handleSaveToProject,
    handleFileRetrieval
  };
};
