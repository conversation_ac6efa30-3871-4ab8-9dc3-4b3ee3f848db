
// Utility functions for chart rendering

/**
 * Format large numbers for display
 */
export const formatNumber = (value: number) => {
  if (value === undefined || value === null || !isFinite(value)) {
    return '0';
  }
  
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(0)}K`;
  }
  return value.toLocaleString();
};

/**
 * Generate a color for chart bars based on index or specific data
 */
export const generateColor = (index: number, categoryName?: string, queryType?: string) => {
  const colors: Record<string, string> = {
    // City colors
    "São Paulo": "#FFD700",      // Gold for São Paulo
    "Rio de Janeiro": "#E91E63", // Pink for Rio
    "Belo Horizonte": "#3F51B5", // Blue for Belo Horizonte

    // Age group colors - purple palette
    "25-34": "#8B5CF6", // Purple
    "35-44": "#9F7AEA", // Lighter purple
    "18-24": "#B794F4", // Even lighter purple
    "45-54": "#6B46C1", // Darker purple
    "55+": "#4A309D",   // Very dark purple

    // Category colors for revenue
    "Electronics": "#3F51B5", // Blue
    "Furniture": "#4CAF50",   // Green
    "Clothing": "#E91E63",    // Pink
    "Groceries": "#FF9800",   // Orange
    "Books": "#9C27B0",       // Purple

    // Default colors
    "default0": "#8B5CF6", // Vivid Purple
    "default1": "#E91E63", // Pink
    "default2": "#F97316", // Orange
    "default3": "#0EA5E9", // Blue
    "default4": "#84CC16"  // Green
  };

  // If we have a category name, try to use its specific color
  if (categoryName && colors[categoryName]) {
    return colors[categoryName];
  }

  // For city queries
  if (queryType === 'city') {
    return colors[`default${index % 5}`];
  }
  
  // For age groups
  if (queryType === 'age') {
    return colors[`default${index % 5}`];
  }
  
  // For revenue/category data
  if (queryType === 'revenue') {
    return colors[`default${index % 5}`];
  }

  // Default color scheme (use index-based)
  return colors[`default${index % 5}`];
};

/**
 * Get a chart title based on the query
 */
export const getChartTitle = (queryString: string | undefined, categoryAxis: string, dataColumns: string[]) => {
  if (!queryString || !categoryAxis) {
    return "Data Visualization";
  }
  
  if (queryString.toLowerCase().includes('city')) {
    return `Data by City`;
  } 
  else if (categoryAxis.toLowerCase().includes('department')) {
    return `Employee Count by Department`;
  }
  else if (categoryAxis.toLowerCase().includes('category')) {
    return `Revenue by Product Category`;
  }
  else if (categoryAxis.toLowerCase().includes('age')) {
    return `Customer Count by Age Group`;
  }
  
  // Default title based on data columns
  if (dataColumns.length === 1) {
    return `${dataColumns[0]} by ${categoryAxis}`;
  } else {
    return `Data Visualization by ${categoryAxis}`;
  }
};

/**
 * Determine chart type from query data
 */
export const getChartType = (queryString: string | undefined, dataRows: any[]) => {
  // Horizontal bar chart for:
  if (queryString?.toLowerCase().includes('city') || 
      queryString?.toLowerCase().includes('top') || 
      dataRows.length <= 5) {
    return 'horizontalBar';
  }
  
  // Age group chart
  if (queryString?.toLowerCase().includes('age')) {
    return 'verticalBar';
  }
  
  // Default vertical bar chart
  return 'verticalBar';
};
