
import { configureStore } from '@reduxjs/toolkit';
import metricReducer from './metricSlice';
import messageReducer from './messageSlice';
import datasetReducer from './datasetSlice';
import fileReducer from './fileSlice';
import errorReducer from './errorSlice';
import projectReducer from './projectSlice';

export const store = configureStore({
  reducer: {
    metric: metricReducer,
    message: messageReducer,
    dataset: datasetReducer,
    file: fileReducer,
    error: errorReducer,
    project: projectReducer, // Add the project reducer
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
