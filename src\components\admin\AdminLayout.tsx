
import React, { useState, useCallback } from 'react';
import { Outlet } from 'react-router-dom';
import AdminSidebar from '@/components/admin/AdminSidebar';
import Header from '@/components/dashboard/Header';

const AdminLayout: React.FC = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  
  // Use useCallback to ensure function reference stability
  const toggleSidebar = useCallback(() => {
    setSidebarCollapsed(prev => !prev);
  }, []);
  
  return (
    <div className="flex flex-col h-screen">
      <Header 
        toggleSidebar={toggleSidebar} 
        sidebarCollapsed={sidebarCollapsed} 
        dashboardName="Admin" 
        rightSidebarVisible={false}
        dashboardType={2}
      />
      <div className="flex flex-1 overflow-hidden bg-gray-50">
        <AdminSidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />
        <main className="flex-1 hover-scrollbar p-6 admin-content">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default React.memo(AdminLayout);
