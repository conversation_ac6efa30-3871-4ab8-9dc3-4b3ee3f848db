import React from 'react';
import Navigation from '@/components/ui/header/Navigation';
import { useAuth } from '@/contexts/AuthContext';

interface SharedLayoutProps {
  children: React.ReactNode;
}

const SharedLayout: React.FC<SharedLayoutProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="pt-16"> {/* Add padding-top to account for fixed header */}
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-medium text-gray-900">Shared Transcript</h1>
            <div className="text-sm text-gray-500">View Only</div>
          </div>
        </div>
        <main className="container mx-auto px-4 py-8">
          {children}
        </main>
      </div>
    </div>
  );
};

export default SharedLayout;


