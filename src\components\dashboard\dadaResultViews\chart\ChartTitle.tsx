
import React from "react";
import { getChartTitle } from "./ChartUtils";

interface ChartTitleProps {
  query?: string;
  categoryAxis: string;
  dataColumns: string[];
}

export const ChartTitle: React.FC<ChartTitleProps> = ({
  query,
  categoryAxis,
  dataColumns
}) => {
  const chartTitle = getChartTitle(query, categoryAxis, dataColumns);
  
  return (
    <div className="text-center font-medium text-gray-800 mb-2">
      {chartTitle}
    </div>
  );
};
