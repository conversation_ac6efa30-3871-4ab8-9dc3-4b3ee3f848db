import { API_KEY, baseUrl } from '../audioTranscript/config';

// Define the Project interface
export interface Project {
  project_id?: number;
  project_name: string;
  project_description: string;
  project_group: string;
  collaboration_channel: string;
  channel_name: string;
  project_owner: string;
  team_members: string | string[];
  project_stakeholders: string | string[]; // Make this required, not optional
  project_status: string;
  created_at?: string | null;
  updated_at?: string | null;
  id?: string; // Backend sometimes returns an additional id field
}

// Define interface for project list item (used in sidebar)
export interface ProjectListItem {
  project_id: number | string;
  project_name: string;
}

// Function to create a new project
export const createProject = async (projectData: Omit<Project, 'project_id'>): Promise<Project> => {
  try {
    // Format team members and stakeholders from string to array if needed
    const formattedData = {
      ...projectData,
      team_members: Array.isArray(projectData.team_members) 
        ? projectData.team_members 
        : projectData.team_members.split('\n').filter(member => member.trim() !== ''),
      // Ensure we're using project_stakeholders, not stakeholders
      project_stakeholders: Array.isArray(projectData.project_stakeholders) 
        ? projectData.project_stakeholders 
        : projectData.project_stakeholders.split('\n').filter(s => s.trim() !== '')
    };

    // Log the data being sent to the API
    console.log('Sending project data to API:', formattedData);

    const response = await fetch(`${baseUrl}/projects/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
      body: JSON.stringify(formattedData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Failed to create project (${response.status})`);
    }

    const responseData = await response.json();
    console.log('API response data:', responseData);
    
    // Normalize the response data to match our interface
    return normalizeProjectData(responseData);
  } catch (error) {
    console.error('Error creating project:', error);
    throw error;
  }
};

// Function to update an existing project
export const updateProject = async (projectData: Project): Promise<Project> => {
  try {
    if (!projectData.project_id) {
      throw new Error('Project ID is required for updates');
    }

    // Format team members and stakeholders from string to array if needed
    const formattedData = {
      ...projectData,
      team_members: Array.isArray(projectData.team_members) 
        ? projectData.team_members 
        : projectData.team_members.split('\n').filter(member => member.trim() !== ''),
      // Ensure we're using project_stakeholders, not stakeholders
      project_stakeholders: Array.isArray(projectData.project_stakeholders) 
        ? projectData.project_stakeholders 
        : projectData.project_stakeholders.split('\n').filter(s => s.trim() !== '')
    };

    // Log the data being sent to the API
    console.log('Sending project update data to API:', formattedData);

    const response = await fetch(`${baseUrl}/projects/${projectData.project_id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
      body: JSON.stringify(formattedData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Failed to update project (${response.status})`);
    }

    const responseData = await response.json();
    console.log('API response data:', responseData);
    
    // Normalize the response data to match our interface
    return normalizeProjectData(responseData);
  } catch (error) {
    console.error('Error updating project:', error);
    throw error;
  }
};

// Function to get all projects
export const getProjects = async (): Promise<Project[]> => {
  try {
    const response = await fetch(`${baseUrl}/projects/`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Failed to fetch projects (${response.status})`);
    }

    const data = await response.json();
    
    // Handle the new response format with "projects" array
    if (data && typeof data === 'object' && 'projects' in data) {
      return data.projects.map(normalizeProjectData);
    }
    
    // Fallback to direct array if the response structure is different
    if (Array.isArray(data)) {
      return data.map(normalizeProjectData);
    }
    
    // Return empty array if no valid data format is found
    console.warn('Unexpected response format from /projects/ endpoint:', data);
    return [];
  } catch (error) {
    console.error('Error fetching projects:', error);
    throw error;
  }
};

// Function to get a project by ID
export const getProjectById = async (projectId: string): Promise<Project> => {
  try {
    const response = await fetch(`${baseUrl}/projects/${projectId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Failed to fetch project (${response.status})`);
    }

    const data = await response.json();
    return normalizeProjectData(data);
  } catch (error) {
    console.error(`Error fetching project with ID ${projectId}:`, error);
    throw error;
  }
};

// Function to delete a project
export const deleteProject = async (projectId: number): Promise<void> => {
  try {
    const response = await fetch(`${baseUrl}/projects/${projectId}`, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Failed to delete project (${response.status})`);
    }
  } catch (error) {
    console.error('Error deleting project:', error);
    throw error;
  }
};

// Function to get project names for sidebar
export const getProjectNames = async (): Promise<ProjectListItem[]> => {
  try {
    // Use the correct endpoint URL
    const response = await fetch(`${baseUrl}/projects/`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage;
      try {
        // Try to parse as JSON
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.detail || `Failed to fetch projects (${response.status})`;
      } catch {
        // If not JSON, use the text directly
        errorMessage = errorText || `Failed to fetch projects (${response.status})`;
      }
      throw new Error(errorMessage);
    }

    const data = await response.json();
    
    // Handle the nested projects array structure
    if (data && data.projects && Array.isArray(data.projects)) {
      return data.projects.map((project: any) => ({
        project_id: project.project_id,
        project_name: project.project_name
      }));
    }
    
    // Fallback to direct array if the response structure changes
    if (Array.isArray(data)) {
      return data.map(project => ({
        project_id: project.project_id,
        project_name: project.project_name
      }));
    }
    
    // Return empty array if no valid data format is found
    console.warn('Unexpected response format from /projects/ endpoint:', data);
    return [];
  } catch (error) {
    console.error('Error fetching project names:', error);
    throw error;
  }
};

// Helper function to normalize project data from API
const normalizeProjectData = (data: any): Project => {
  // Handle team_members that might be a single string with comma-separated values
  let teamMembers = data.team_members;
  if (teamMembers && !Array.isArray(teamMembers) && typeof teamMembers === 'string') {
    teamMembers = teamMembers.split(',').map(email => email.trim());
  }
  
  // Handle project_stakeholders that might be in different formats
  let projectStakeholders = data.project_stakeholders || [];
  if (projectStakeholders && !Array.isArray(projectStakeholders) && typeof projectStakeholders === 'string') {
    projectStakeholders = projectStakeholders.split(',').map(email => email.trim());
  }
  
  return {
    project_id: data.project_id,
    project_name: data.project_name,
    project_description: data.project_description,
    project_group: data.project_group,
    collaboration_channel: data.collaboration_channel,
    channel_name: data.channel_name || data.collaboration_channel?.split(':')[1] || '',
    project_owner: data.project_owner,
    team_members: teamMembers,
    project_stakeholders: projectStakeholders,
    project_status: data.project_status,
    created_at: data.created_at,
    updated_at: data.updated_at,
    id: data.id,
  };
};






