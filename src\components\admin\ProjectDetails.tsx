import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit } from 'lucide-react';
import { getProjectById, Project } from '@/services/api/admin/projectService';
import { toast } from 'sonner';
import ProjectForm from './ProjectForm';

const ProjectDetails: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    fetchProject();
  }, [projectId]);

  const fetchProject = async () => {
    if (!projectId) return;
    
    try {
      setIsLoading(true);
      const projectData = await getProjectById(projectId);
      setProject(projectData);
    } catch (error) {
      console.error('Error fetching project:', error);
      toast.error('Failed to load project details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveProject = (updatedProject: any) => {
    setProject(updatedProject);
    setIsEditing(false);
    fetchProject(); // Refresh data from API
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const handleBackClick = () => {
    navigate('/Admin/menu');
  };

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Loading project details...</p>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Project not found</p>
        <Button onClick={handleBackClick} variant="outline" className="mt-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Projects
        </Button>
      </div>
    );
  }

  if (isEditing) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button onClick={handleBackClick} variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-xl font-bold ml-4">Edit Project</h1>
        </div>
        <ProjectForm 
          onSave={handleSaveProject} 
          onCancel={handleCancelEdit}
          initialData={project}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Button onClick={handleBackClick} variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-xl font-bold ml-4">Project Details</h1>
        </div>
        <Button onClick={() => setIsEditing(true)} variant="outline">
          <Edit className="mr-2 h-4 w-4" />
          Edit Project
        </Button>
      </div>

      <div className="border rounded-md p-6">
        <div className="grid grid-cols-2 gap-y-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Project ID</h3>
            <p className="mt-1 font-medium">{project.project_id}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Status</h3>
            <p className="mt-1 font-medium">{project.project_status}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Name</h3>
            <p className="mt-1 font-medium">{project.project_name}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Owner</h3>
            <p className="mt-1 font-medium">{project.project_owner}</p>
          </div>
          <div className="col-span-2">
            <h3 className="text-sm font-medium text-gray-500">Description</h3>
            <p className="mt-1 font-medium">{project.project_description}</p>
          </div>
          <div className="col-span-2">
            <h3 className="text-sm font-medium text-gray-500">Collaboration Channel</h3>
            <p className="mt-1 font-medium">{project.collaboration_channel}</p>
          </div>
          <div className="col-span-2">
            <h3 className="text-sm font-medium text-gray-500">Team Members</h3>
            {Array.isArray(project.team_members) && project.team_members.length > 0 ? (
              <p className="mt-1 font-medium">{project.team_members.join(', ')}</p>
            ) : (
              <p className="mt-1 text-gray-500">No team members</p>
            )}
          </div>
          <div className="col-span-2">
            <h3 className="text-sm font-medium text-gray-500">Stakeholders</h3>
            {Array.isArray(project.project_stakeholders) && project.project_stakeholders.length > 0 ? (
              <p className="mt-1 font-medium">{project.project_stakeholders.join(', ')}</p>
            ) : (
              <p className="mt-1 text-gray-500">No stakeholders</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetails;

