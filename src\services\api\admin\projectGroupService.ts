import { API_KEY, baseUrl } from '../audioTranscript/config';

// Define the ProjectGroup interface
export interface ProjectGroup {
  group_name: string;
  description?: string | null;
  created_at?: string;
  id?: string;
}

// Function to get all project groups
export const getProjectGroups = async (): Promise<string[]> => {
  try {
    const response = await fetch(`${baseUrl}/project-groups`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Failed to fetch project groups (${response.status})`);
    }

    const data = await response.json();
    
    // Extract only group_name from the response
    if (data && data.groups && Array.isArray(data.groups)) {
      return data.groups.map((group: ProjectGroup) => group.group_name);
    }
    
    // Return empty array if no valid data format is found
    console.warn('Unexpected response format from /project-groups endpoint:', data);
    return [];
  } catch (error) {
    console.error('Error fetching project groups:', error);
    throw error;
  }
};

// Function to create a new project group
export const createProjectGroup = async (groupName: string): Promise<ProjectGroup> => {
  try {
    if (!groupName.trim()) {
      throw new Error('Group name is required');
    }

    const response = await fetch(`${baseUrl}/project-groups/quick-create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
      body: JSON.stringify({ group_name: groupName })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Failed to create project group (${response.status})`);
    }

    const responseData = await response.json();
    console.log('API response data:', responseData);
    
    return responseData;
  } catch (error) {
    console.error('Error creating project group:', error);
    throw error;
  }
};

// Function to get a project group by name
export const getProjectGroupByName = async (groupName: string): Promise<ProjectGroup | null> => {
  try {
    const groups = await getProjectGroups();
    const exists = groups.includes(groupName);
    
    if (!exists) {
      return null;
    }
    
    // Since we only have the group name, we return a minimal object
    return { group_name: groupName };
  } catch (error) {
    console.error(`Error checking project group ${groupName}:`, error);
    throw error;
  }
};