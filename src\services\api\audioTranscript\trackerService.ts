import { toast } from 'sonner';
import { API_KEY, baseUrl } from './config';

// Cache for tracker project names
let trackerProjectsCache = {
  data: null as string[] | null,
  timestamp: 0,
  ttl: 5 * 60 * 1000 // 5 minutes
};

interface UploadToTrackerParams {
  fileId: number;
  projectId: string;
  projectName: string;
}

interface TrackerResponse {
  success: boolean;
  message: string;
  items?: any[];
}

/**
 * Normalizes API response data to ensure consistent property naming
 * Converts both snake_case API properties and camelCase properties to camelCase
 */
function normalizeTrackerItemData(item: any) {
  return {
    id: item.id,
    projectName: item.project_name || item.projectName,
    activity: item.activity,
    activityType: item.activity_type || item.activityType,
    activityStatus: item.activity_status || item.activityStatus,
    assignedTo: item.assigned_to || item.assignedTo,
    assignedOn: item.assigned_on || item.assignedOn,
    eta: item.eta,
    meetingId: item.meeting_id || item.meetingId,
    comments: item.comments,
    filename: item.filename,
    tags: item.tags,
    // Any additional fields
    availableTeamMembers: item.available_team_members || item.availableTeamMembers,
    modifiedBy: item.modified_by || item.modifiedBy
  };
}

/**
 * Uploads a file to the tracker for a specific project
 * @param params Object containing fileId, projectId and projectName
 * @returns Promise with the response from the API
 */
export const uploadToTracker = async (
  params: UploadToTrackerParams
): Promise<TrackerResponse> => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }

    // Encode the project name for the URL
    const encodedProjectName = encodeURIComponent(params.projectName);
    
    // Construct the URL with query parameters
    const url = `${baseUrl}/tracker/${params.fileId}?project_name=${encodedProjectName}`;
    
    console.log("Making API request to:", url);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    console.log("API response status:", response.status);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error("API error response:", errorData);
      throw new Error(errorData.detail || errorData.message || `Failed to upload to tracker (${response.status})`);
    }

    const data = await response.json();
    console.log("API success response:", data);
    
    // Invalidate the cache after successful upload
    invalidateTrackerCache();
    
    return data as TrackerResponse;
  } catch (error) {
    console.error('Error uploading to tracker:', error);
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    throw new Error(errorMessage);
  }
};

/**
 * Gets all tracker items for a specific project
 * @param projectName The name of the project
 * @returns Promise with the tracker items and transcript options
 */
export const getTrackerItemsByProjectName = async (projectName: string) => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }

    // Encode the project name for the URL
    const encodedProjectName = encodeURIComponent(projectName);
    
    // Always include include_options=true parameter
    const url = `${baseUrl}/tracker?project_name=${encodedProjectName}&include_options=true`;
    console.log("Fetching tracker items from:", url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    console.log("API response status:", response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error("API error response:", errorData);
      throw new Error(errorData.detail || errorData.message || `Failed to get tracker items (${response.status})`);
    }

    const data = await response.json();
    console.log("Tracker items received:", data);
    
    // Handle the new response format with tracker_items array and transcript_options
    let trackerItems = [];
    let transcriptOptions = [];
    
    if (data && data.tracker_items && Array.isArray(data.tracker_items)) {
      // Normalize all items
      trackerItems = data.tracker_items.map(normalizeTrackerItemData);
    } else if (Array.isArray(data)) {
      // Fallback to direct array if the response structure is different
      trackerItems = data.map(normalizeTrackerItemData);
    } else {
      console.error("Unexpected data format:", data);
      throw new Error("Invalid data format received from API");
    }
    
    // Extract transcript options if available
    if (data && data.transcript_options && Array.isArray(data.transcript_options)) {
      transcriptOptions = data.transcript_options;
    }
    
    // Return both tracker items and transcript options
    return {
      trackerItems,
      transcriptOptions
    };
  } catch (error) {
    console.error('Error getting tracker items:', error);
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    throw new Error(errorMessage);
  }
};

/**
 * Invalidates the tracker projects cache
 */
export const invalidateTrackerCache = () => {
  trackerProjectsCache.data = null;
  trackerProjectsCache.timestamp = 0;
};

/**
 * Gets all unique project names from the tracker
 * @param forceRefresh Force a refresh of the cache
 * @returns Promise with an array of unique project names
 */
export const getUniqueTrackerProjects = async (forceRefresh = false): Promise<string[]> => {
  try {
    // Use cached data if available and not expired
    const now = Date.now();
    if (!forceRefresh && 
        trackerProjectsCache.data && 
        now - trackerProjectsCache.timestamp < trackerProjectsCache.ttl) {
      console.log('Using cached tracker projects');
      return trackerProjectsCache.data;
    }
    
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }
    
    // Always include include_options=true parameter
    const url = `${baseUrl}/tracker?include_options=true`;
    console.log("Fetching all tracker items from:", url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
      cache: 'no-cache' // More standard than 'no-store'
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || errorData.message || `Failed to get tracker items (${response.status})`);
    }

    const data = await response.json();
    
    // Extract tracker items from the response
    let trackerItems = [];
    if (data && data.tracker_items && Array.isArray(data.tracker_items)) {
      trackerItems = data.tracker_items;
    } else if (Array.isArray(data)) {
      trackerItems = data;
    } else {
      console.error("Unexpected data format:", data);
      throw new Error("Invalid data format received from API");
    }
    
    // Extract unique project names
    const projectNames = new Set<string>();
    trackerItems.forEach((item: any) => {
      if (item.projectName || item.project_name) {
        projectNames.add(item.projectName || item.project_name);
      }
    });
    
    const projectNamesArray = Array.from(projectNames);
    console.log("Found unique project names:", projectNamesArray);
    
    // Update the cache
    trackerProjectsCache = {
      data: projectNamesArray,
      timestamp: now,
      ttl: trackerProjectsCache.ttl
    };
    
    return projectNamesArray;
  } catch (error) {
    console.error('Error getting tracker projects:', error);
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    throw new Error(errorMessage);
  }
};

/**
 * Updates multiple tracker items in a batch
 * @param items Array of tracker items to update
 * @returns Promise with the response from the API
 */
export const updateTrackerItemsBatch = async (items: any[]): Promise<TrackerResponse> => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }
    
    const url = `${baseUrl}/tracker/batch`;
    console.log("Making batch update API request to:", url);
    
    // Ensure each item has the modified_by field
    const itemsWithModifier = items.map(item => {
      // Create a properly formatted item with both camelCase and snake_case fields
      // The backend checks for both formats
      return {
        ...item,
        // Include both formats to ensure the backend picks it up
        modified_by: item.modified_by || 'unknown_user',
        modifiedBy: item.modified_by || item.modifiedBy || 'unknown_user',
        // Format dates properly
        eta: item.eta || null
      };
    });
    
    console.log("Sending items:", JSON.stringify(itemsWithModifier, null, 2));

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
      body: JSON.stringify(itemsWithModifier)
    });

    console.log("API response status:", response.status);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error("API error response:", errorData);
      throw new Error(errorData.detail || errorData.message || `Failed to update tracker items (${response.status})`);
    }

    const data = await response.json();
    console.log("API success response:", JSON.stringify(data, null, 2));
    
    // Transform the response to match your frontend model
    if (data && data.items && Array.isArray(data.items)) {
      data.items = data.items.map((item: any) => normalizeTrackerItemData(item));
    }
    
    // Invalidate the cache after successful update
    invalidateTrackerCache();
    
    return data as TrackerResponse;
  } catch (error) {
    console.error('Error updating tracker items:', error);
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    throw new Error(errorMessage);
  }
};

/**
 * Interface for change history item
 */
export interface ChangeHistoryItem {
  id: string;
  tracker_id: string;
  old_values: Record<string, any>;
  new_values: Record<string, any>;
  modifiedOn: string;
  modifiedBy: string;
}

/**
 * Interface for change history response
 */
export interface ChangeHistoryResponse {
  change_history: ChangeHistoryItem[];
}

/**
 * Gets the change history for a specific tracker item
 * @param trackerId The ID of the tracker item
 * @returns Promise with the change history
 */
export const getChangeHistory = async (trackerId: string): Promise<ChangeHistoryItem[]> => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }
    
    const url = `${baseUrl}/change-history?tracker_id=${trackerId}`;
    console.log("Fetching change history from:", url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    console.log("API response status:", response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error("API error response:", errorData);
      throw new Error(errorData.detail || errorData.message || `Failed to get change history (${response.status})`);
    }

    const data = await response.json() as ChangeHistoryResponse;
    console.log("Change history received:", data);
    
    return data.change_history;
  } catch (error) {
    console.error('Error getting change history:', error);
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    toast.error('Failed to get change history', {
      description: errorMessage
    });
    throw error;
  }
};
