
import React, { memo, useState } from 'react';
import { TableRow, TableCell } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { History, MessageSquare, Calendar as CalendarIcon } from 'lucide-react';
import { TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import TrackerStatusCell from './TrackerStatusCell';
import { TrackerItem } from '@/hooks/useTrackerItems';
import { ChangeHistoryDialog } from './ChangeHistoryDialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

interface TrackerTableRowProps {
  item: TrackerItem;
  isExpanded: boolean;
  rowId: string;
  onInputChange: (id: string, field: keyof TrackerItem, value: string) => void;
  onStatusChange: (id: string, value: string) => void;
  onOpenComments: (rowId: string) => void;
  toggleRowExpansion: (id: string) => void;
}

const TrackerTableRow: React.FC<TrackerTableRowProps> = ({
  item,
  isExpanded,
  rowId,
  onInputChange,
  onStatusChange,
  onOpenComments,
  toggleRowExpansion
}) => {
  const [isHistoryDialogOpen, setIsHistoryDialogOpen] = useState(false);
  
  // Process the team members array - only if it exists
  const teamMembers = item.availableTeamMembers && item.availableTeamMembers.length > 0 
    ? item.availableTeamMembers[0].split(',').map(email => email.trim())
    : [];

  return (
    <>
      <TableRow className={`hover:bg-gray-50 ${isExpanded ? 'bg-blue-50' : ''}`}>
        <TableCell className="border border-gray-300 px-2 py-1 align-middle p-1">{item.projectName}</TableCell>
        <TableCell 
          className="border border-gray-300 px-2 py-1 align-middle p-0"
          onDoubleClick={() => toggleRowExpansion(rowId)}
          style={{ minWidth: '250px', maxWidth: '400px' }}
        >
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="relative">
                  <Input 
                    value={typeof item.activity === 'string' ? item.activity : 
                            (item.activity ? JSON.stringify(item.activity) : '')}
                    onChange={(e) => onInputChange(rowId, 'activity', e.target.value)}
                    className="h-6 text-xs px-1.5 w-full font-normal border-none shadow-none"
                    style={{ 
                      fontSize: 'inherit', 
                      lineHeight: 'inherit', 
                      fontFamily: 'inherit',
                      minWidth: '250px'
                    }}
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs break-words">
                  {typeof item.activity === 'string' ? item.activity : 
                   (item.activity ? JSON.stringify(item.activity) : '')}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </TableCell>
        <TableCell className="border border-gray-300 px-2 py-1 align-middle p-1">{item.activityType}</TableCell>
        <TableCell className="border border-gray-300 px-2 py-1 align-middle text-center p-1">
          {item.assignedOn ? new Date(item.assignedOn).toLocaleDateString() : ''}
        </TableCell>
        <TableCell className="border border-gray-300 px-2 py-1 align-middle p-0" style={{ minWidth: '120px' }}>
          <TrackerStatusCell 
            status={item.activityStatus} 
            onStatusChange={(value) => onStatusChange(rowId, value)} 
          />
        </TableCell>
        <TableCell className="border border-gray-300 px-2 py-1 align-middle p-0">
          {/* Replace Input with Select dropdown while maintaining the same styling */}
          {teamMembers.length > 0 ? (
            <Select
              value={item.assignedTo}
              onValueChange={(value) => onInputChange(rowId, 'assignedTo', value)}
            >
              <SelectTrigger 
                className="h-6 text-sm px-1.5 min-w-[90px] font-normal border-none shadow-none bg-transparent focus:ring-0 focus:ring-offset-0" 
                style={{ 
                  boxShadow: 'none', 
                  outline: 'none',
                  paddingRight: '20px'
                }}
              >
                <SelectValue placeholder="Assign to..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Unassigned</SelectItem>
                {teamMembers.map((email, index) => (
                  <SelectItem key={index} value={email}>{email}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Input 
              value={item.assignedTo} 
              onChange={(e) => onInputChange(rowId, 'assignedTo', e.target.value)}
              className="h-6 text-xs px-1.5 w-full font-normal border-none shadow-none"
              style={{ fontSize: 'inherit', lineHeight: 'inherit', fontFamily: 'inherit' }}
            />
          )}
        </TableCell>
        <TableCell className="border border-gray-300 px-2 py-1 align-middle p-0" style={{ minWidth: '100px' }}>
          <Popover>
            <PopoverTrigger asChild>
              <div className="flex items-center w-full cursor-pointer relative"> {/* Added relative positioning */}
                <Input 
                  value={item.eta ? format(new Date(item.eta), 'dd-MM-yyyy') : ''} 
                  readOnly
                  className="h-6 text-xs px-1.5 w-full font-normal border-none shadow-none cursor-pointer"
                  style={{ fontSize: 'inherit', lineHeight: 'inherit', fontFamily: 'inherit' }}
                />
                {!item.eta && <CalendarIcon className="h-4 w-4 absolute right-2 text-gray-400" />}
              </div>
            </PopoverTrigger>
            <PopoverContent className="p-0" align="start">
              <Calendar
                mode="single"
                selected={item.eta ? new Date(item.eta) : undefined}
                onSelect={(date) => {
                  if (date) {
                    // Format the date as ISO string for API compatibility
                    const formattedDate = date.toISOString();
                    onInputChange(rowId, 'eta', formattedDate);
                    
                    // Close the popover
                    setTimeout(() => {
                      // Click outside the popover to close it
                      const outsideElement = document.querySelector('body');
                      if (outsideElement) {
                        outsideElement.click();
                      }
                    }, 100);
                  }
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </TableCell>
        <TableCell className="border border-gray-300 px-2 py-1 align-middle text-center p-0">{item.meetingId}</TableCell>
        <TableCell 
          className="border border-gray-300 px-2 py-1 align-middle cursor-pointer p-1"
          onClick={() => onOpenComments(rowId)}
        >
          <div className="relative flex items-center h-full w-full">
            <div className="flex-1 truncate text-xs">
              {item.comments ? item.comments.substring(0, 20) + (item.comments.length > 20 ? '...' : '') : ''}
            </div>
            {item.comments && (
              <div className="text-blue-500 ml-1">
                <MessageSquare size={14} />
              </div>
            )}
          </div>
        </TableCell>
        <TableCell className="border border-gray-300 px-2 py-1 align-middle p-1">
          {item.tags || ''}
        </TableCell>
        <TableCell className="border border-gray-300 px-2 py-1 align-middle p-0">
          <div className="flex justify-center items-center">
            <button 
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              title="View change history"
              onClick={() => setIsHistoryDialogOpen(true)}
            >
              <History size={16} />
            </button>
            
            <ChangeHistoryDialog 
              isOpen={isHistoryDialogOpen}
              onClose={() => setIsHistoryDialogOpen(false)}
              trackerId={item.id}
              activityName={item.activity}
            />
          </div>
        </TableCell>
      </TableRow>
      
      {isExpanded && (
        <TableRow key={`expanded-${rowId}`} className="bg-blue-50">
          <TableCell colSpan={11} className="border border-gray-300 px-4 py-2 p-0">
            <div className="font-medium mb-1">Full Activity:</div>
            <div className="bg-white p-2 rounded border border-gray-200">
              <textarea
                value={item.activity}
                onChange={(e) => onInputChange(rowId, 'activity', e.target.value)}
                className="w-full min-h-[100px] p-2 text-sm border-none focus:outline-none focus:ring-1 focus:ring-blue-500 rounded"
                placeholder="Enter activity details..."
              />
            </div>
          </TableCell>
        </TableRow>
      )}
    </>
  );
};

export default memo(TrackerTableRow);
