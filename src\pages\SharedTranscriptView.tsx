import React from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import SharedLayout from '@/layouts/SharedLayout';
import SharedTranscriptContent from '@/components/shared/SharedTranscriptContent';
import { useAuth } from '@/contexts/AuthContext';

// Type guard to check if folderType is valid
const isValidFolderType = (folderType: string): folderType is 'personal' | 'project' => {
  return folderType === 'personal' || folderType === 'project';
};

export const SharedTranscriptView: React.FC = () => {
  const { folderType, fileName } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, checkSharedAccess } = useAuth();

  // Check if there's a valid user session in localStorage
  const hasValidSession = () => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      const userData = JSON.parse(storedUser);
      const currentTime = new Date().getTime();
      return userData.expiryTime && userData.expiryTime > currentTime;
    }
    return false;
  };

  // Validate folder type and filename
  if (!folderType || !fileName || !isValidFolderType(folderType)) {
    navigate('/transcript');
    return null;
  }

  // Check both isAuthenticated and localStorage session
  if (!isAuthenticated && !hasValidSession()) {
    navigate('/signin', { 
      state: { 
        from: location.pathname,
        isSharedView: true 
      } 
    });
    return null;
  }

  return (
    <SharedLayout>
      <div className="bg-white rounded-lg shadow">
        <SharedTranscriptContent 
          fileName={fileName}
          folderType={folderType}
        />
      </div>
    </SharedLayout>
  );
};

export default SharedTranscriptView;


