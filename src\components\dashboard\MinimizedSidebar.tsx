import React from 'react';
import { Folder, PanelLeftClose, PanelLeftOpen } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface MinimizedSidebarProps {
  onPersonalFilesClick: () => void;
  onProjectFilesClick: () => void;
  isCollapsed: boolean;
  onToggle: () => void;
}

const MinimizedSidebar: React.FC<MinimizedSidebarProps> = ({
  onPersonalFilesClick,
  onProjectFilesClick,
  isCollapsed,
  onToggle
}) => {
  return (
    <div className="w-16 bg-gray-200 border-r border-gray-200 flex flex-col items-center">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button 
              onClick={onToggle}
              className="w-10 h-10 mt-2 rounded-md flex items-center justify-center hover:bg-gray-100"
              aria-label={isCollapsed ? "Open sidebar" : "Close sidebar"}
            >
                <PanelLeftOpen size={20} className="h-5 w-5 text-gray-600" />
            </button>
          </TooltipTrigger>
          <TooltipContent side="right">
            <p>{isCollapsed ? "Open sidebar" : "Close sidebar"}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <div className="flex flex-col space-y-4 mt-4">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button 
                onClick={onPersonalFilesClick}
                className="w-10 h-10 rounded-md flex items-center justify-center hover:bg-gray-100 text-blue-600"
              >
                <Folder size={20} />
              </button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>Personal Files</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button 
                onClick={onProjectFilesClick}
                className="w-10 h-10 rounded-md flex items-center justify-center hover:bg-gray-100 text-green-600"
              >
                <Folder size={20} />
              </button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>Project Files</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
};

export default MinimizedSidebar;








