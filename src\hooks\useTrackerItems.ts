
import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { getTrackerItemsByProjectName } from '@/services/api/audioTranscript/trackerService';
import { useAuth } from '@/contexts/AuthContext';
import { REFRESH_TRACKER_PROJECTS_EVENT } from '@/components/dashboard/fileUpload/TrackerDialog';

export interface CommentItem {
  id: string;
  text: string;
  user: string;
  userInitials: string;
  timestamp: Date;
}

export interface TrackerItem {
  id: string;
  projectName: string;
  activity: string;
  activityType: string;
  assignedOn: string;
  activityStatus: string;
  assignedTo: string;
  eta: string | null;
  meetingId: string;
  comments: string;
  commentItems?: CommentItem[];
  filename?: string;
  tags?: string;
  availableTeamMembers?: string[]; // Team members available for assignment
}

export interface TranscriptOption {
  value: string;
  label: string;
  filename: string;
  meeting_id: string;
  transcript_id: string;
}

export function useTrackerItems(projectName: string | undefined) {
  const [trackerItems, setTrackerItems] = useState<TrackerItem[]>([]);
  const [transcriptOptions, setTranscriptOptions] = useState<TranscriptOption[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [modifiedItemIds, setModifiedItemIds] = useState<Set<string>>(new Set());
  const displayProjectName = projectName ? decodeURIComponent(projectName) : '';
  const { user } = useAuth(); // Get the current user from auth context
  
  // Use a ref to track the latest fetch request
  const latestRequestRef = useRef<AbortController | null>(null);
  
  // Memoize the unique transcripts list for performance
  const transcripts = useMemo(() => 
    transcriptOptions.map(option => option.label), 
  [transcriptOptions]);

  // Format date for API
  const formatDateForAPI = useCallback((dateStr: string): string | null => {
    if (!dateStr) return null;
    
    try {
      // If it's already in ISO format, return it
      if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(dateStr)) {
        return dateStr;
      }
      
      // Otherwise, convert to ISO format
      const date = new Date(dateStr);
      return date.toISOString();
    } catch (e) {
      console.error('Error formatting date:', e);
      return null;
    }
  }, []);

  // Handle input changes
  const handleInputChange = useCallback((id: string, field: keyof TrackerItem, value: string) => {
    setTrackerItems(prevItems => 
      prevItems.map(item => 
        item.id === id ? { ...item, [field]: value } : item
      )
    );
    
    // Mark this item as modified
    setModifiedItemIds(prev => new Set(prev).add(id));
  }, []);

  // Handle status changes
  const handleStatusChange = useCallback((id: string, value: string) => {
    setTrackerItems(prevItems => 
      prevItems.map(item => 
        item.id === id ? { ...item, activityStatus: value } : item
      )
    );
    
    // Mark this item as modified
    setModifiedItemIds(prev => new Set(prev).add(id));
  }, []);

  // Add a comment to an item
  const addComment = useCallback((id: string, commentText: string, userName: string, userInitials: string) => {
    const timestamp = new Date();
    const commentId = `comment-${Date.now()}`;
    
    setTrackerItems(prevItems => 
      prevItems.map(item => {
        if (item.id === id) {
          const newCommentItems = [
            ...(item.commentItems || []),
            {
              id: commentId,
              text: commentText,
              user: userName,
              userInitials,
              timestamp
            }
          ];
          
          return { 
            ...item, 
            commentItems: newCommentItems,
            comments: newCommentItems.map(c => `${c.user}: ${c.text}`).join('\n')
          };
        }
        return item;
      })
    );
    
    // Mark this item as modified
    setModifiedItemIds(prev => new Set(prev).add(id));
  }, []);

  // Add a function to get only modified items
  const getModifiedItems = useCallback(() => {
    return trackerItems.filter(item => modifiedItemIds.has(item.id)).map(item => {
      // Ensure all fields are properly formatted for the API
      return {
        ...item,
        // Make sure ETA is properly formatted as ISO string or null if empty
        eta: item.eta && item.eta.trim() !== '' 
          ? formatDateForAPI(item.eta)  // Convert to proper format
          : null,
        // Add the current user's email as modified_by
        modified_by: user?.email || user?.username || 'unknown_user',
      };
    });
  }, [trackerItems, modifiedItemIds, user, formatDateForAPI]);

  // Function to fetch tracker items
  const fetchTrackerItems = useCallback(async (abortController?: AbortController) => {
    if (!projectName) {
      setIsLoading(false);
      return;
    }
    
    setIsLoading(true);
    try {
      // If there's an existing request, abort it
      if (latestRequestRef.current) {
        latestRequestRef.current.abort();
      }
      
      // Create a new abort controller
      const controller = abortController || new AbortController();
      latestRequestRef.current = controller;
      
      const response = await getTrackerItemsByProjectName(projectName);
      
      // Only update state if not aborted
      if (!controller.signal.aborted) {
        if (response) {
          // Set tracker items
          setTrackerItems(response.trackerItems.map(item => ({
            ...item,
            // Parse comment items if they exist
            commentItems: item.comments ? item.comments.split('\n').map((comment, index) => {
              const parts = comment.split(': ');
              const user = parts[0] || 'Unknown';
              const text = parts.slice(1).join(': ') || '';
              return {
                id: `comment-${item.id}-${index}`,
                text,
                user,
                userInitials: user.split(' ').map(part => part[0]).join('').toUpperCase(),
                timestamp: new Date()
              };
            }) : []
          })));
          
          // Set transcript options
          setTranscriptOptions(response.transcriptOptions || []);
        } else {
          setTrackerItems([]);
          setTranscriptOptions([]);
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Fetch aborted');
      } else {
        console.error('Error fetching tracker items:', error);
        toast.error('Failed to load tracker items');
        setTrackerItems([]);
        setTranscriptOptions([]);
      }
    } finally {
      setIsLoading(false);
    }
  }, [projectName]);

  // Fetch tracker items when project name changes
  useEffect(() => {
    const controller = new AbortController();
    fetchTrackerItems(controller);
    
    // Listen for refresh events
    const handleRefresh = () => {
      console.log("Refresh tracker projects event received");
      fetchTrackerItems();
    };
    
    window.addEventListener(REFRESH_TRACKER_PROJECTS_EVENT, handleRefresh);
    
    return () => {
      // Clean up by aborting any in-flight requests
      controller.abort();
      window.removeEventListener(REFRESH_TRACKER_PROJECTS_EVENT, handleRefresh);
    };
  }, [projectName, fetchTrackerItems]);

  // Add reload function
  const reloadTrackerItems = useCallback(() => {
    fetchTrackerItems();
  }, [fetchTrackerItems]);

  return {
    trackerItems,
    setTrackerItems,
    transcriptOptions,
    isLoading,
    displayProjectName,
    transcripts,
    handleInputChange,
    handleStatusChange,
    addComment,
    getModifiedItems,
    modifiedItemIds,
    setModifiedItemIds,
    reloadTrackerItems
  };
}
