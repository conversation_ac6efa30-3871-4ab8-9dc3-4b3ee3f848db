
import * as React from "react"
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  minWidth?: string
  maxHeight?: string
  className?: string
}

export function DataTable<TData, TValue>({
  columns,
  data,
  minWidth = "1200px",
  maxHeight = "100%",
  className,
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <div 
      className={`w-full tracker-table-scroll ${className || ''}`}
      style={{ 
        maxHeight,
        overflow: 'auto',  // Enable both scrollbars
        position: 'relative',
        border: '1px solid #e2e8f0',
        borderRadius: '4px',
        // Force scrollbar visibility
        scrollbarWidth: 'auto',
        scrollbarColor: '#888 #f1f1f1',
      }}
    >
      <table 
        style={{ 
          minWidth,
          width: '100%',
          borderCollapse: 'collapse',
        }}
      >
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th 
                  key={header.id}
                  className="bg-teal-600 text-white border border-gray-300 p-2 text-left text-sm font-medium"
                  style={{
                    minWidth: '150px',
                  }}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.length ? (
            table.getRowModel().rows.map((row) => (
              <tr
                key={row.id}
                className="hover:bg-gray-50"
              >
                {row.getVisibleCells().map((cell) => (
                  <td
                    key={cell.id}
                    className="border border-gray-300 p-2 text-sm"
                  >
                    {flexRender(
                      cell.column.columnDef.cell,
                      cell.getContext()
                    )}
                  </td>
                ))}
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={table.getAllColumns().length}
                className="h-24 text-center"
              >
                No results.
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  )
}
