
import React, { useEffect } from 'react';
import { useLocation, useParams, useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/dashboard/layout/DashboardLayout';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { setCurrentTranscript, clearCurrentTranscript } from '@/stores/fileSlice';
import { getTranscriptByFileName } from '@/services/api/audioTranscript';

interface DashboardProps {
  initialDashboard?: 1 | 2 | 3;
}

const Dashboard: React.FC<DashboardProps> = ({ initialDashboard }) => {
  const location = useLocation();
  const { folderType, fileName } = useParams();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // Get files from Redux store
  const personalFiles = useAppSelector(state => state.file.personalFiles);
  const projectFiles = useAppSelector(state => state.file.projectFiles);

  const getDashboardType = () => {
    if (initialDashboard) {
      return initialDashboard;
    } else if (location.state && location.state.dashboard) {
      const dashboardType = Number(location.state.dashboard);
      if ([1, 2, 3].includes(dashboardType)) {
        return dashboardType as 1 | 2 | 3;
      }
    }
    return 1;
  };

  const dashboardType = getDashboardType();

  useEffect(() => {
    const handleTranscriptAccess = async () => {
      // Only proceed if we have both folderType and fileName in the URL
      if (folderType && fileName) {
        // Check if this is a direct URL access (shared link)
        const isSharedAccess = location.pathname.startsWith('/transcript/') && 
                             location.pathname.split('/').length >= 4;
        
        // If it's a direct shared access, fetch the transcript data
        if (isSharedAccess) {
          try {
            // Fetch transcript data directly from API
            const transcriptData = await getTranscriptByFileName(
              fileName,
              folderType as 'personal' | 'project'
            );
            
            if (transcriptData) {
              // Set the current transcript in Redux
              dispatch(setCurrentTranscript({
                data: transcriptData,
                fileName,
                folderType: folderType as 'personal' | 'project',
                isExisting: true
              }));
              return; // Exit early as we've handled the shared access
            }
          } catch (error) {
            console.error('Error fetching shared transcript:', error);
            navigate('/transcript');
            return;
          }
        }
        
        // For normal access, validate if file exists in the store
        const files = folderType === 'personal' ? personalFiles : projectFiles;
        const fileExists = files.includes(fileName);

        if (!fileExists && !isSharedAccess) {
          // If file doesn't exist and not shared access, redirect to /transcript
          navigate('/transcript');
          return;
        }

        // Set the current transcript in Redux
        dispatch(setCurrentTranscript({
          data: null, // This will be populated by FileUploadView when mounted
          fileName,
          folderType: folderType as 'personal' | 'project',
          isExisting: true
        }));
      } else {
        // If we're at the base /transcript route without params, clear any existing transcript
        if (location.pathname === '/transcript') {
          dispatch(clearCurrentTranscript());
        }
      }
    };
    
    handleTranscriptAccess();
  }, [folderType, fileName, personalFiles, projectFiles, navigate, dispatch, location.pathname]);

  return <DashboardLayout initialDashboard={dashboardType} />;
};

export default Dashboard;
