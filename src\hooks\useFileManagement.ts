
import { useState, useEffect } from 'react';
import { useAppDispatch } from '@/hooks/useRedux';
import { useNavigate } from 'react-router-dom';
import { setCurrentTranscript } from '@/stores/fileSlice';
import { getTranscriptByFileName } from '@/services/api/audioTranscript';
import { setError } from '@/stores/errorSlice';
import { toast } from 'sonner';
import { FolderType } from '@/services/api/audioTranscript/types';
import { useFileCacheManager } from './useFileCacheManager';

export const useFileManagement = (dashboardType: 1 | 2 | 3) => {
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  
  // Use our new cache manager instead of direct API calls
  const { 
    fetchAllFileData, 
    fileData, 
    fetchStatus 
  } = useFileCacheManager();
  
  const isLoadingFiles = fetchStatus.personalFiles === 'loading' || 
                         fetchStatus.projectFiles === 'loading' ||
                         fetchStatus.trackerProjects === 'loading';

  // Only fetch files once on mount for dashboard type 2
  useEffect(() => {
    let isMounted = true;
    
    const loadFiles = async () => {
      if (isMounted && dashboardType === 2) {
        await fetchAllFileData();
      }
    };
    
    loadFiles();
    
    return () => {
      isMounted = false;
    };
  }, [dashboardType, fetchAllFileData]);

  const handleFileClick = async (fileName: string, folderType: FolderType) => {
    if (folderType === 'tracker') return;
    
    setIsLoading(true);
    try {
      const transcriptData = await getTranscriptByFileName(fileName, folderType);
      if (transcriptData) {
        dispatch(setCurrentTranscript({
          data: transcriptData,
          fileName,
          folderType,
          isExisting: true
        }));
        
        navigate(`/transcript/${folderType}/${fileName}`);
      } else {
        toast.error('Failed to load file');
      }
    } catch (error) {
      console.error('Error loading file:', error);
      dispatch(setError({
        message: error instanceof Error ? error.message : 'An unexpected error occurred',
        statusCode: error.status || 500
      }));
    } finally {
      setIsLoading(false);
      toast.success('File loaded Successfully');
    }
  };

  return {
    isLoading,
    isLoadingFiles,
    fetchFiles: fetchAllFileData,
    handleFileClick,
    fileData
  };
};
