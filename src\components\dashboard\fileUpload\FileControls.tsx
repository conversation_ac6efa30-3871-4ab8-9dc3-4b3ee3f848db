
import React, { useState } from 'react';
import { FileControlsProps } from './types';
import { Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ShareDialog } from './ShareDialog';

const FileControls = ({
  selectedFile,
  isLoading,
  handleSubmit,
  clearFileInput,
  folderType
}: FileControlsProps) => {
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);

  return (
    <>
      <div className="mt-2 flex justify-between">
        <div className="flex space-x-2">
          <Button
            variant={selectedFile ? (isLoading ? 'secondary' : 'greenmind') : 'secondary'}
            disabled={!selectedFile || isLoading}
            onClick={handleSubmit}
          >
            {isLoading ? 'Processing...' : 'Transcript'}
          </Button>
        </div>

        <Button
          variant={selectedFile ? (isLoading ? 'secondary' : 'white') : 'secondary'}
          disabled={!selectedFile || isLoading}
          className="flex items-center"
          onClick={() => setIsShareDialogOpen(true)}
        >
          <Share2 className="h-4 w-4 mr-2" />
          Share
        </Button>
      </div>

      {selectedFile && (
        <ShareDialog
          isOpen={isShareDialogOpen}
          onClose={() => setIsShareDialogOpen(false)}
          fileName={selectedFile.name}
          folderType={folderType}
        />
      )}
    </>
  );
};

export default FileControls;
