
// Re-export all necessary functionality without creating duplicated exports
// Do not re-export individual types that are already exported from types.ts
export * from './uploadService';
export * from './fileManagementService';
export * from './exportService';
export * from './trackerService';

// Import and re-export the types explicitly to avoid duplication
// Note: We are not re-exporting FolderType here since it's already exported from types.ts
import { 
  SummaryData,
  TranscriptResponse,
  FileListResponse
} from './types';

// Use 'export type' syntax for type re-exports when isolatedModules is enabled
export type {
  SummaryData,
  TranscriptResponse,
  FileListResponse
};

// Export types.ts separately to allow direct imports of all types including FolderType
export * as types from './types';
