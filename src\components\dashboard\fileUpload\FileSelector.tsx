
import { useNavigate } from 'react-router-dom';
import React, { useRef, useState } from 'react';
import { Upload } from 'lucide-react';
import { ExtendedUploadedFile } from '../modelsExtension';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import ProjectSelector from './ProjectSelector';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface FileSelectorProps {
  selectedFile: ExtendedUploadedFile | null;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  clearFileInput: () => void;
  isTranscriptLoaded?: boolean;
  onProjectSelect?: (project: { id: string; name: string }) => void;
  onTopicChange?: (topic: string) => void;
  transcriptData?: {
    transcript: any[];
    file_id?: number;
    audioUrl?: string;
    summary: string;
    actionItems: any[];
    importantPoints: string[];
    openQuestions: any[];
  };
}

const FileSelector = ({ 
  selectedFile, 
  handleFileChange, 
  clearFileInput,
  isTranscriptLoaded = false,
  onProjectSelect,
  onTopicChange,
  transcriptData
}: FileSelectorProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedProject, setSelectedProject] = useState<{ id: string; name: string } | null>(null);
  const [topic, setTopic] = useState('');
  const navigate = useNavigate();

  const handleClearAndNavigate = () => {
    clearFileInput();
    // Navigate back to the base transcript route
    navigate('/transcript');
  };

  const handleConfirmNewFile = () => {
    handleClearAndNavigate();
    fileInputRef.current?.click();
    setShowConfirmDialog(false);
  };

  const triggerFileInput = () => {
    if (isTranscriptLoaded) {
      setShowConfirmDialog(true);
    } else {
      fileInputRef.current?.click();
    }
  };

  const handleProjectSelect = (projectId: string, projectName: string) => {
    const project = { id: projectId, name: projectName };
    setSelectedProject(project);
    if (onProjectSelect) {
      onProjectSelect(project);
    }
  };

  const handleTopicChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTopic(e.target.value);
    if (onTopicChange) {
      onTopicChange(e.target.value);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="file-upload" className="block mb-2 text-sm font-medium text-gray-700">
          Upload Files
        </Label>
        <div className="flex gap-4">
          <div
            className="flex-1 border border-gray-400 rounded-md p-2 flex items-center justify-between cursor-pointer"
            onClick={triggerFileInput}
          >
            <div className="text-gray-500 text-sm">
              {selectedFile ? selectedFile.name : 'Upload files (video, audio, .zip)'}
            </div>
            <Upload size={20} className={selectedFile ? "text-blue-500" : "text-gray-400"} />
            <input
              id="file-upload"
              ref={fileInputRef}
              type="file"
              className="hidden"
              onChange={handleFileChange}
              accept="video/*,audio/*,.zip"
            />
          </div>
        </div>
      </div>

      <div className="flex gap-4">
        <div className="flex-1">
          <Label htmlFor="transcript-name" className="block mb-2 text-sm font-medium text-gray-700">
            Transcript Name
          </Label>
          <Input
            id="transcript-name"
            placeholder="Transcript Name"
            value={selectedFile?.name || ''}
            className="w-full"
            readOnly
          />
        </div>
        <div className="w-50">
          <Label htmlFor="file-id" className="block mb-2 text-sm font-medium text-gray-700">
            Transcript Id
          </Label>
          <Input
            id="file-id"
            placeholder="Transcript Id"
            value={selectedFile?.file_id || (transcriptData && transcriptData.file_id) || ''}
            className="w-full bg-gray-400 text-black"
            readOnly
          />
        </div>
      </div>

      <div>
        <Label htmlFor="project-select" className="block mb-2 text-sm font-medium text-gray-700">
          Select Project
        </Label>
        <ProjectSelector onProjectSelect={handleProjectSelect} />
      </div>

      <div>
        <Label htmlFor="topic" className="block mb-2 text-sm font-medium text-gray-700">
          Topic
        </Label>
        <Input
          id="topic"
          placeholder="Enter topic"
          value={topic}
          onChange={handleTopicChange}
          className="w-full"
        />
      </div>

      {/* Confirm dialog for replacing current file */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Replace current file?</DialogTitle>
            <DialogDescription>
              You already have a file loaded. Uploading a new file will replace the current one.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleConfirmNewFile}>
              Continue
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FileSelector;
