
import React, { useEffect } from "react";
import type { QueryResultData } from "@/components/dashboard/models";
import { processChartData } from "./chart/ChartDataProcessor";
import { VerticalBarChart, HorizontalBarChart } from "./chart/ChartTypes";
import { ChartTitle } from "./chart/ChartTitle";

interface ResultChartProps {
  size?: "large" | "default" | "small";
  database?: string;
  queryResult?: QueryResultData | null;
}

const ResultChart = ({ 
  size = "default", 
  database = "SQL", 
  queryResult
}: ResultChartProps) => {
  // Set appropriate heights based on size prop
  const height = size === "default" ? "h-64" : size === "large" ? "h-96" : "h-40";
  const textSize = size === "default" ? "text-sm" : "text-xs";

  // Debug logs to identify potential issues
  useEffect(() => {
    console.log("ResultChart rendering with queryResult:", queryResult);
    if (queryResult && queryResult.tableData) {
      console.log("Table data:", queryResult.tableData);
      console.log("Columns:", queryResult.tableData.columns);
      console.log("Row count:", queryResult.tableData.rows ? queryResult.tableData.rows.length : 0);
    }
  }, [queryResult]);
  
  if (!queryResult || !queryResult.tableData || !queryResult.tableData.rows || queryResult.tableData.rows.length === 0) {
    console.log("No queryResult data available for chart");
    return (
      <div className={`${height} bg-gray-50 rounded border border-gray-200 flex items-center justify-center`}>
        <p className={`text-gray-500 ${textSize}`}>No data available for chart visualization</p>
      </div>
    );
  }

  // Process chart data
  const chartDataResult = processChartData(queryResult);
  const {
    chartData,
    categoryAxis,
    dataColumns,
    maxValue,
    hasSufficientData,
    chartType,
    queryType
  } = chartDataResult;
  
  console.log("Processed chart data:", { 
    chartDataLength: chartData.length,
    categoryAxis, 
    dataColumns, 
    chartType,
    hasSufficientData,
    firstDataPoint: chartData.length > 0 ? chartData[0] : null
  });
  
  if (!hasSufficientData || chartData.length === 0) {
    console.log("Insufficient data for chart visualization");
    return (
      <div className={`${height} bg-gray-50 rounded border border-gray-200 flex items-center justify-center`}>
        <p className={`text-gray-500 ${textSize}`}>Insufficient data for chart visualization</p>
      </div>
    );
  }

  return (
    <div className={`${height} bg-white rounded border border-gray-200 relative p-2`}>
      <ChartTitle 
        query={queryResult.query} 
        categoryAxis={categoryAxis} 
        dataColumns={dataColumns} 
      />
      <div className="w-full h-[85%]">
        {chartType === 'verticalBar' ? (
          <VerticalBarChart
            chartData={chartData}
            categoryAxis={categoryAxis}
            dataColumns={dataColumns}
            queryType={queryType}
            maxValue={maxValue}
          />
        ) : (
          <HorizontalBarChart
            chartData={chartData}
            categoryAxis={categoryAxis}
            dataColumns={dataColumns}
            queryType={queryType}
            maxValue={maxValue}
          />
        )}
      </div>
    </div>
  );
};

// Export the named component for regular imports
export { ResultChart };

// Default export for lazy loading
export default ResultChart;
