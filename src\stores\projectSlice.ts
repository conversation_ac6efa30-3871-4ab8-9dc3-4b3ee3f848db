import { createSlice } from '@reduxjs/toolkit';

interface ProjectState {
  refreshTrigger: number;
}

const initialState: ProjectState = {
  refreshTrigger: 0,
};

const projectSlice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    refreshSidebarProjects: (state) => {
      // Increment the trigger to cause a re-fetch
      state.refreshTrigger += 1;
    },
  },
});

export const { refreshSidebarProjects } = projectSlice.actions;
export default projectSlice.reducer;