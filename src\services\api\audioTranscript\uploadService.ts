
import { toast } from 'sonner';
import { TranscriptResponse } from './types';
import { API_KEY, baseUrl } from './config';

export const uploadAudioForTranscript = async (file: File, email?: string, username?: string): Promise<TranscriptResponse | null> => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }

    const formData = new FormData();
    formData.append('file', file);
    if (email) formData.append('email', email);
    if (username) formData.append('username', username);

    const response = await fetch(`${baseUrl}/upload-audio/`, {
      method: 'POST',
      body: formData,
      headers: {
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('API Error Response:', errorData);
      throw new Error(JSON.stringify(errorData));
    }

    const data = await response.json();
    console.log("Raw API response:", data);

    // Preserve the email and username from the request if they're null in the response
    if (data.email === null && email) {
      data.email = email;
    }

    if (data.username === null && username) {
      data.username = username;
    }

    // Ensure the audio URL is properly formatted and available
    if (data.audio_url) {
      data.audioUrl = data.audio_url.startsWith('/') 
        ? `${baseUrl}${data.audio_url}` 
        : data.audio_url;
    }

    // We don't need this check anymore since we're standardizing on audioUrl
    // and the backend is now correctly returning email and username

    console.log("Processed response with audio URL:", data);
    
    return data as TranscriptResponse;
  } catch (error) {
    console.error('Error uploading audio file:', error);
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      try {
        const parsedError = JSON.parse(error.message);
        errorMessage = parsedError.detail || error.message;
      } catch {
        errorMessage = error.message;
      }
    }
    toast.error('Failed to process audio', {
      description: errorMessage
    });
    return null;
  }
};
