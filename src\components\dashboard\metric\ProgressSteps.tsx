
import React from "react"
import { Step } from "@/stores/metricSlice"

interface ProgressStepsProps {
  currentStep: Step
  publishCompleted: boolean
}

export const ProgressSteps: React.FC<ProgressStepsProps> = ({ currentStep, publishCompleted }) => {
  const steps: Step[] = ["define", "select", "review", "approve", "publish"]
  
  const isStepActive = (step: Step): boolean => {
    return step === currentStep;
  }
  
  const isStepCompleted = (step: Step): boolean => {
    if (publishCompleted && step === "publish") return true;
    
    const currentIndex = steps.indexOf(currentStep);
    const stepIndex = steps.indexOf(step);
    
    return stepIndex < currentIndex;
  }

  const getProgressWidth = (): string => {
    const currentIndex = steps.indexOf(currentStep);
    const totalSteps = steps.length - 1; // -1 because we want 100% at the last step
    const progress = (currentIndex / totalSteps) * 100;
    return `${progress}%`;
  }
  
  return (
    <div className="flex justify-between mb-8 relative">
      {/* Background line (gray) */}
      <div className="absolute top-1/2 left-0 right-0 h-1 bg-gray-200 -translate-y-1/2 z-0"></div>
      
      {/* Progress line (green) */}
      <div 
        className="absolute top-1/2 left-0 h-1 bg-green-500 -translate-y-1/2 z-0 transition-all duration-300 ease-in-out"
        style={{ width: getProgressWidth() }}
      ></div>
      
      {steps.map((step, index) => (
        <div key={step} className="flex flex-col items-center relative z-10">
          <div 
            className={`w-16 h-10 flex items-center justify-center uppercase text-xs font-medium mb-1 ${
              isStepActive(step) 
                ? "bg-green-100 text-green-600 border border-green-300" 
                : isStepCompleted(step) 
                  ? "bg-green-100 text-green-600 border border-green-300" 
                  : "bg-gray-100 text-gray-500 border border-gray-300"
            } rounded transition-all duration-300`}
          >
            {step}
          </div>
        </div>
      ))}
    </div>
  )
}
