
import { ResultSql } from "./ResultSql"
import { Suspense, lazy, useEffect } from "react"
import type { QueryResultData } from "@/components/dashboard/models"
import { Loader2 } from "lucide-react"

// Lazy load the heavier components
const ResultTable = lazy(() => import("./ResultTable"))
const ResultChart = lazy(() => import("./ResultChart"))

interface TabContentProps {
  activeTab: string
  size?: "large" | "default" | "small"
  database?: string
  queryResult?: QueryResultData | null
  isPowerQuery?: boolean // Prop to check if it's a power query
}

const TabLoader = () => (
  <div className="flex items-center justify-center h-64">
    <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
    <span className="ml-2 text-gray-600">Loading...</span>
  </div>
)

export const TabContent = ({ activeTab, size = "default", database = "SQL", queryResult, isPowerQuery = false }: TabContentProps) => {
  const padding = size === "large" ? "p-4" : "p-2"

  // Debug logs to track query result data
  useEffect(() => {
    console.log("TabContent rendering with activeTab:", activeTab, "queryResult:", queryResult, "isPowerQuery:", isPowerQuery);
  }, [activeTab, queryResult, isPowerQuery]);

  // If it's a power query and activeTab is "sql", reset to "table"
  useEffect(() => {
    if (isPowerQuery && activeTab === "sql") {
      console.log("Power query detected with SQL tab - this should not happen as TabNavigation should prevent this");
      // This won't directly change the prop, but logs the situation
      // The parent component should handle this case
    }
  }, [isPowerQuery, activeTab]);

  return (
    <div className={padding}>
      {activeTab === "table" && (
        <Suspense fallback={<TabLoader />}>
          <ResultTable size={size} database={database} queryResult={queryResult} />
        </Suspense>
      )}
      {activeTab === "chart" && (
        <Suspense fallback={<TabLoader />}>
          <ResultChart size={size} database={database} queryResult={queryResult} />
        </Suspense>
      )}
      {/* Only render SQL tab for non-power queries */}
      {activeTab === "sql" && <ResultSql size={size} database={database} queryResult={queryResult} isPowerQuery={isPowerQuery} />}
    </div>
  )
}
