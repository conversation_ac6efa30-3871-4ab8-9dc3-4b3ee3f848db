
export interface Meeting {
  id: number;
  title: string;
  date: string;
  time: string;
  attendees: string[];
  agenda: string[];
  summary: string;
}

export interface Message {
  type: 'query' | 'response';
  content: string;
  minimized: boolean;
  queryResult?: QueryResultData;
}

export interface Project {
  id: number;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  status: 'active' | 'completed' | 'on hold';
  tasks: Task[];
}

export interface Task {
  id: number;
  name: string;
  description: string;
  status: 'to do' | 'in progress' | 'completed';
  assignee: string;
  dueDate: string;
}

export interface ChatTopic {
  id: number;
  title: string;
  messages: Message[];
}

export interface QueryResultData {
  query: string;
  tableData: TableData;
}

export interface TableData {
  columns: string[];
  rows: Record<string, any>[];
}

export type TranscriptTab = 'transcript' | 'summary' | 'actionItems' | 'notes' | 'openQuestions';

export interface UploadedFile {
  name: string;
  type: string;
  size: number;
  section: TranscriptTab[];
  url?: string;
}

export interface TextSizeStyle {
  size: string;
  spacing: string;
}

export interface TextSizeStyles {
  [key: string]: TextSizeStyle;
}

export interface ContentProps {
  dashboardType: number;
  textSize?: 'small' | 'medium' | 'large';
  searchQuery?: string;
  headerSearchQuery?: string;
  showMetricScreen?: boolean;
  showDatasetScreen?: boolean;
  setShowMetricScreen: (show: boolean) => void;
  setShowDatasetScreen: (show: boolean) => void;
}

export const textSizeStyles: TextSizeStyles = {
  small: {
    size: "text-sm",
    spacing: "space-y-3"
  },
  medium: {
    size: "text-base",
    spacing: "space-y-4"
  },
  large: {
    size: "text-lg",
    spacing: "space-y-5"
  }
};
