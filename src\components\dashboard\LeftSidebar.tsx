
import React, { useCallback, memo, useState, useMemo } from 'react';
import SidebarNav from './sidebar/SidebarNav';
import SidebarContent from './sidebar/SidebarContent';
import { useSidebarItems } from '@/hooks/useSidebarItems';
import { useTextSize } from '@/hooks/useTextSize';
import { useSearchFilter } from '@/hooks/useSearchFilter';
import {PanelLeftClose, PanelLeftOpen, FileText, FolderClosed } from 'lucide-react';

interface LeftSidebarProps {
  collapsed: boolean;
  searchQuery: string;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  searchResults?: string[];
  onNewChat?: () => void;
  onTextSizeChange?: (size: 'small' | 'medium' | 'large') => void;
  dashboardType: 1 | 2 | 3;
  onDatasetIconClick: () => void;
  isDatasetScreenOpen: boolean;
  onToggle: () => void;
}

const LeftSidebar = ({ 
  collapsed,
  searchQuery, // This will be renamed to sidebarSearchQuery
  handleSearch, // This will be renamed to handleSidebarSearch
  onTextSizeChange,
  onDatasetIconClick,
  dashboardType,
  isDatasetScreenOpen,
  onToggle
}: LeftSidebarProps) => {
  // Add local state for sidebar search
  const [sidebarSearchQuery, setSidebarSearchQuery] = useState('');
  
  const {
    filteredItems,
    toggleItem,
    addChatToItem,
    addNewItem,
    filterItems
  } = useSidebarItems(dashboardType);

  const handleSidebarSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSidebarSearchQuery(value);
    filterItems(value, dashboardType); // This only affects sidebar items
  }, [dashboardType, filterItems]);

  const { textSize, handleTextSizeChange } = useTextSize('medium');

  useSearchFilter({
    searchQuery,
    dashboardType,
    filterItems
  }, [searchQuery, dashboardType]);

  const handleTextSizeChangeWithCallback = useCallback((size: 'small' | 'medium' | 'large') => {
    handleTextSizeChange(size);
    if (onTextSizeChange) {
      onTextSizeChange(size);
    }
  }, [handleTextSizeChange, onTextSizeChange]);

  // Memoize SidebarContent props to prevent unnecessary re-renders
  const sidebarContentProps = useMemo(() => ({
    collapsed,
    searchQuery: dashboardType === 2 ? searchQuery : sidebarSearchQuery,
    handleSearch: dashboardType === 2 ? handleSearch : handleSidebarSearch,
    filteredItems,
    toggleItem,
    addChatToItem,
    addNewItem,
    dashboardType,
    onToggle
  }), [
    collapsed, 
    searchQuery, 
    sidebarSearchQuery, 
    handleSearch, 
    handleSidebarSearch, 
    filteredItems, 
    toggleItem, 
    addChatToItem, 
    addNewItem, 
    dashboardType,
    onToggle
  ]);

  // Dashboard-2 specific rendering
  if (dashboardType === 2) {
    return (
      <div className="flex h-full">
        <SidebarContent {...sidebarContentProps} />
      </div>
    );
  }

  // Dashboard-1 and Dashboard-3 rendering
  return (
    <div className="flex h-full">
      <SidebarNav 
        activeDashboard={dashboardType} 
        textSize={textSize} 
        handleTextSizeChange={handleTextSizeChangeWithCallback} 
        onDatasetIconClick={onDatasetIconClick}
        isDatasetScreenOpen={isDatasetScreenOpen}
      />
      <SidebarContent {...sidebarContentProps} />
    </div>
  );
};

export default memo(LeftSidebar);
