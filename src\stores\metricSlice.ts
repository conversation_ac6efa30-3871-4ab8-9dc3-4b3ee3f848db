
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { format } from 'date-fns';

export type Step = 'define' | 'select' | 'review' | 'approve' | 'publish';

export interface MetricFormData {
  metricName: string;
  metricDescription: string;
  metricFormula: string;
  selectedTables: string[];
  reviewStatus: string;
  reviewedBy: string;
  reviewerEmail: string;
  reviewDate: string;
  approvalStatus: string;
  approvedBy: string;
  approverEmail: string;
  approvalDate: string;
}

interface MetricState {
  currentStep: Step;
  showSuccessMessage: boolean;
  publishCompleted: boolean;
  showMetricScreen: boolean;
  formData: MetricFormData;
}

const initialState: MetricState = {
  currentStep: 'define',
  showSuccessMessage: false,
  publishCompleted: false,
  showMetricScreen: false,
  formData: {
    metricName: '',
    metricDescription: '',
    metricFormula: '',
    selectedTables: [],
    reviewStatus: 'Pending',
    reviewedBy: '',
    reviewerEmail: '',
    reviewDate: format(new Date(), 'dd-MMM-yyyy'),
    approvalStatus: 'Pending',
    approvedBy: '',
    approverEmail: '',
    approvalDate: format(new Date(), 'dd-MMM-yyyy'),
  }
};

export const metricSlice = createSlice({
  name: 'metric',
  initialState,
  reducers: {
    setShowMetricScreen: (state, action: PayloadAction<boolean>) => {
      state.showMetricScreen = action.payload;
    },
    setCurrentStep: (state, action: PayloadAction<Step>) => {
      state.currentStep = action.payload;
    },
    setShowSuccessMessage: (state, action: PayloadAction<boolean>) => {
      state.showSuccessMessage = action.payload;
    },
    setPublishCompleted: (state, action: PayloadAction<boolean>) => {
      state.publishCompleted = action.payload;
    },
    updateFormData: (state, action: PayloadAction<{name: string; value: any}>) => {
      const { name, value } = action.payload;
      (state.formData as any)[name] = value;
    },
    handleTableSelect: (state, action: PayloadAction<string>) => {
      const tableName = action.payload;
      if (state.formData.selectedTables.includes(tableName)) {
        state.formData.selectedTables = state.formData.selectedTables.filter(t => t !== tableName);
      } else {
        state.formData.selectedTables.push(tableName);
      }
    },
    handleNext: (state) => {
      if (state.currentStep === 'define') state.currentStep = 'select';
      else if (state.currentStep === 'select') state.currentStep = 'review';
      else if (state.currentStep === 'review') state.currentStep = 'approve';
      else if (state.currentStep === 'approve') {
        state.currentStep = 'publish';
        state.showSuccessMessage = true;
      }
    },
    handleBack: (state) => {
      if (state.currentStep === 'select') state.currentStep = 'define';
      else if (state.currentStep === 'review') state.currentStep = 'select';
      else if (state.currentStep === 'approve') state.currentStep = 'review';
      else if (state.currentStep === 'publish') {
        state.currentStep = 'approve';
        state.publishCompleted = false;
      }
    },
    handleCloseSuccessMessage: (state) => {
      state.showSuccessMessage = false;
      state.publishCompleted = true;
    }
  }
});

export const { 
  setShowMetricScreen, 
  setCurrentStep, 
  setShowSuccessMessage, 
  setPublishCompleted,
  updateFormData,
  handleTableSelect,
  handleNext,
  handleBack,
  handleCloseSuccessMessage
} = metricSlice.actions;

export default metricSlice.reducer;
