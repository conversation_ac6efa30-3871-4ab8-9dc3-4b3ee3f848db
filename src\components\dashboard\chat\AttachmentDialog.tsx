
import React from "react"
import { <PERSON>c<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

interface AttachmentDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const AttachmentDialog = ({ 
  isOpen, 
  onOpenChange, 
  handleFileChange 
}: AttachmentDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload File</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-12">
            <Paperclip className="mb-4 text-gray-400" size={24} />
            <p className="text-sm text-gray-500 mb-2">Drag and drop files here or click to browse</p>
            <input
              type="file"
              id="file-upload"
              multiple
              className="hidden"
              onChange={handleFileChange}
            />
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => document.getElementById('file-upload')?.click()}
            >
              Browse Files
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AttachmentDialog;
