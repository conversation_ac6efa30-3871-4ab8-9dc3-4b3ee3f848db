
import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { CommentItem } from '@/hooks/useTrackerItems';

export function useTrackerComments(
  addComment: (id: string, commentText: string, userName: string, userInitials: string) => void
) {
  const [activeCommentId, setActiveCommentId] = useState<string | null>(null);
  const [newCommentText, setNewCommentText] = useState<string>('');
  const { user } = useAuth();
  
  // Get user initials helper function
  const getUserInitials = useCallback((username: string | undefined): string => {
    if (!username) return 'U';
    
    const nameParts = username.split(' ');
    
    if (nameParts.length === 1) {
      return `${nameParts[0].charAt(0)}${nameParts[0].charAt(nameParts[0].length - 1)}`.toUpperCase();
    }
    
    const firstName = nameParts[0];
    const lastName = nameParts[nameParts.length - 1];
    
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  }, []);
  
  const userInitials = getUserInitials(user?.username);

  // Handle adding a new comment
  const handleAddComment = useCallback(() => {
    if (!activeCommentId || !newCommentText.trim()) return;
    
    addComment(
      activeCommentId,
      newCommentText,
      user?.username || 'Anonymous User',
      userInitials
    );
    
    setNewCommentText('');
  }, [activeCommentId, newCommentText, user?.username, userInitials, addComment]);

  return {
    activeCommentId,
    setActiveCommentId,
    newCommentText,
    setNewCommentText,
    handleAddComment,
    userInitials
  };
}
