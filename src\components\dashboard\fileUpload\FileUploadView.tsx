
import React, { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { useTranscriptUpload } from '@/hooks/useTranscriptUpload';
import FileSelector from './FileSelector';
import FileControls from './FileControls';
import TranscriptTabNavigation from './TranscriptTabNavigation';
import TranscriptTabView from './TranscriptTabView';
import SaveDialog from './SaveDialog';
import { FileUploadProps } from './types';

const FileUploadView: React.FC<FileUploadProps> = ({ onFileUpload }) => {
  const [selectedProject, setSelectedProject] = useState<{ id: string; name: string } | null>(null);
  
  const handleProjectSelect = (project: { id: string; name: string }) => {
    setSelectedProject(project);
  };

  const {
    selectedFile,
    activeTab,
    isSubmitted,
    isLoading,
    isSaveDialogOpen,
    currentFolderType,
    transcriptData,
    availableTabs,
    setActiveTab,
    setIsSaveDialogOpen,
    handleFileChange,
    clearFileInput,
    handleSubmit,
    handleSaveClick,
    handleSaveToPersonal,
    handleSaveToProject
  } = useTranscriptUpload(onFileUpload);

  const scrollbarShowStyles = `
    .scrollbar-show::-webkit-scrollbar {
      display: block;
    }
  `;

  return (
    <div className="flex flex-col h-full overflow-y-auto scrollbar-show">
      <style>{scrollbarShowStyles}</style>
      <div className="p-4 border-b border-gray-200">
        <FileSelector 
          selectedFile={selectedFile} 
          handleFileChange={handleFileChange}
          clearFileInput={clearFileInput}
          isTranscriptLoaded={isSubmitted}
          onProjectSelect={handleProjectSelect}
          transcriptData={transcriptData}
        />
        
        <FileControls
          selectedFile={selectedFile}
          isLoading={isLoading}
          handleSubmit={handleSubmit}
          clearFileInput={clearFileInput}
          folderType={currentFolderType}
        />

        {isLoading && (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
            <span className="ml-2 text-gray-600">Processing your file...</span>
          </div>
        )}
      </div>

      {isSubmitted && selectedFile && !isLoading && (
        <>
          <TranscriptTabNavigation 
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            availableTabs={availableTabs}
            onSaveClick={handleSaveClick}
            fileName={selectedFile.name}
            transcriptData={transcriptData}
            isSharedView={false}
            selectedProject={selectedProject}
            fileId={transcriptData.file_id}
          />

          <TranscriptTabView 
            activeTab={activeTab}
            transcriptData={transcriptData}
          />

          {isSaveDialogOpen && (
            <SaveDialog 
              isOpen={isSaveDialogOpen}
              onClose={() => setIsSaveDialogOpen(false)}
              fileName={selectedFile.name}
              transcriptData={transcriptData}
              onSaveToPersonal={handleSaveToPersonal}
              onSaveToProject={handleSaveToProject}
            />
          )}
        </>
      )}
    </div>
  );
};

export default FileUploadView;
