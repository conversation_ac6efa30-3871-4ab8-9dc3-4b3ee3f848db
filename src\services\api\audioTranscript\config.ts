
// Get values from environment variables with validation
export const API_KEY = import.meta.env.VITE_API_KEY;
export const API_URL = import.meta.env.VITE_API_URL;

if (!API_URL) {
  console.error('API_URL is not defined in environment variables');
}

if (!API_KEY) {
  console.error('API_KEY is not defined in environment variables');
}

// Ensure API_URL doesn't end with a slash
export const baseUrl = API_URL?.endsWith('/') ? API_URL.slice(0, -1) : API_URL;
