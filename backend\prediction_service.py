
import torch
from typing import List
from transformers import GPT2LMHeadModel, GPT2Tokenizer
import re

class PredictionService:
    def __init__(self):
        self.model = GPT2LMHeadModel.from_pretrained('core/fine_tuned_gpt2')
        self.tokenizer = GPT2Tokenizer.from_pretrained('core/fine_tuned_gpt2_tokenizer')
        self.model.eval()  # Set the model to evaluation mode
        self.model.config.pad_token_id = self.model.config.eos_token_id

    def generate_predictions(self, query: str, limit: int = 3) -> List[str]:
        """
        Generate predictions based on the input query using the language model.
        
        Args:
            query (str): The input query to generate predictions for
            limit (int): Maximum number of predictions to return
            
        Returns:
            List[str]: A list of prediction strings
        """
        # Check if this is a special power keyword query
        if query.startswith("@"):
            # Handle power keyword suggestions based on query stage
            return self._handle_power_keyword_query(query)
        
        try:
            return self._generate_model_predictions(query, limit)
        except Exception as e:
            print(f"Error generating predictions: {e}")
            return self.get_fallback_predictions()
    
    def _handle_power_keyword_query(self, query: str) -> List[str]:
        """Handle different stages of power keyword queries"""
        # Strip double @ if present for backward compatibility
        clean_query = query.strip()
        if clean_query.startswith("@@"):
            clean_query = "@" + clean_query[2:]
            
        # Check for command suggestions based on power keyword pattern
        power_keyword_match = re.match(r'^@(\w+)\s*$', clean_query)
        if power_keyword_match:
            power_keyword = power_keyword_match.group(1)
            return self.get_commands(power_keyword)
        
        # Check for parameter suggestions based on power keyword and command pattern
        param_match = re.match(r'^@(\w+)\s+(\w+)\s*$', clean_query)
        if param_match:
            power_keyword = param_match.group(1)
            command = param_match.group(2)
            return self.get_parameters(power_keyword, command)
            
        # Initial @ query with no keywords yet
        return self.get_power_keywords(clean_query)
    
    def _generate_model_predictions(self, query: str, limit: int) -> List[str]:
        """Generate predictions using the language model"""
        input_ids = self.tokenizer.encode(query, return_tensors='pt')
        
        outputs = self.model.generate(
            input_ids,
            max_length=len(input_ids[0]) + 10,
            num_return_sequences=limit,
            do_sample=True,
            temperature=0.7,
            top_k=50,
            pad_token_id=self.tokenizer.eos_token_id,
            attention_mask=torch.ones(input_ids.shape, device=input_ids.device),
            no_repeat_ngram_size=2,
        )
        
        # Decode and clean up the predictions
        predictions = [self.tokenizer.decode(out, skip_special_tokens=True) for out in outputs]
        
        # Remove any predictions that are identical to the input query
        predictions = [p for p in predictions if p.strip() != query.strip()]
        
        # If we don't have enough predictions, generate fallback suggestions
        if len(predictions) < limit:
            fallback_predictions = self.get_fallback_predictions(query)
            predictions.extend(fallback_predictions)
            
        return predictions[:limit]  # Return top predictions

    def get_power_keywords(self, query: str) -> List[str]:
        """
        Get power keyword suggestions for queries starting with @
        """
        try:
            # Strip @ prefix for backend query
            clean_query = query.replace("@", "").strip()
            
            return self._fetch_power_keywords_from_db(clean_query)
                
        except Exception as e:
            print(f"Error getting power keywords: {e}")
            return ["@select", "@filter", "@aggregate"]
    
    def _fetch_power_keywords_from_db(self, clean_query: str) -> List[str]:
        """Fetch power keywords from the database"""
        try:
            # Use the database connection from the main app
            from main import cur
            
            # Execute query to get power keywords
            cur.execute("SELECT DISTINCT power_keyword FROM keywords")
            results = cur.fetchall()
            
            # Format results as list of strings with @ prefix
            power_keywords = [f"@{row[0]}" for row in results]
            
            # Filter by provided query if there is one
            if clean_query:
                power_keywords = [kw for kw in power_keywords if clean_query.lower() in kw.lower()[1:]]
            
            return power_keywords[:5]  # Limit to 5 suggestions
            
        except Exception as e:
            print(f"Database error in get_power_keywords: {e}")
            # Return mock data for development
            mock_keywords = ["@select", "@filter", "@aggregate", "@join", "@sort"]
            if clean_query:
                mock_keywords = [kw for kw in mock_keywords if clean_query.lower() in kw.lower()[1:]]
            return mock_keywords
    
    def get_commands(self, power_keyword: str) -> List[str]:
        """
        Get command suggestions for a given power keyword
        """
        try:
            from main import cur
            
            try:
                # Execute query to get commands
                cur.execute("SELECT DISTINCT command FROM keywords WHERE power_keyword = %s", (power_keyword,))
                results = cur.fetchall()
                
                # Format results as complete command strings
                commands = [f"@{power_keyword} {row[0]}" for row in results]
                
                return commands[:5]  # Limit to 5 suggestions
                
            except Exception as e:
                print(f"Database error in get_commands: {e}")
                return self._get_mock_commands(power_keyword)
                
        except Exception as e:
            print(f"Error getting commands: {e}")
            return [f"@{power_keyword} command1", f"@{power_keyword} command2"]
    
    def _get_mock_commands(self, power_keyword: str) -> List[str]:
        """Get mock command suggestions when database is unavailable"""
        if power_keyword.lower() == "select":
            return [f"@select all", f"@select where", f"@select top"]
        elif power_keyword.lower() == "filter":
            return [f"@filter equals", f"@filter contains", f"@filter greater"]
        else:
            return [f"@{power_keyword} command1", f"@{power_keyword} command2"]
    
    def get_parameters(self, power_keyword: str, command: str) -> List[str]:
        """
        Get parameter suggestions for a given power keyword and command
        """
        try:
            from main import cur
            
            try:
                # Execute query to get parameters
                cur.execute("SELECT DISTINCT parameters FROM keywords WHERE power_keyword = %s AND command = %s", 
                           (power_keyword, command))
                results = cur.fetchall()
                
                # Format results as complete command strings with parameters
                parameters = [f"@{power_keyword} {command} {row[0]}" for row in results]
                
                return parameters[:5]  # Limit to 5 suggestions
                
            except Exception as e:
                print(f"Database error in get_parameters: {e}")
                return self._get_mock_parameters(power_keyword, command)
                
        except Exception as e:
            print(f"Error getting parameters: {e}")
            return [f"@{power_keyword} {command} param1", f"@{power_keyword} {command} param2"]
    
    def _get_mock_parameters(self, power_keyword: str, command: str) -> List[str]:
        """Get mock parameter suggestions when database is unavailable"""
        if power_keyword.lower() == "select" and command.lower() == "where":
            return [f"@select where id=", f"@select where name=", f"@select where date="]
        elif power_keyword.lower() == "filter" and command.lower() == "equals":
            return [f"@filter equals value", f"@filter equals null", f"@filter equals empty"]
        else:
            return [f"@{power_keyword} {command} param1", f"@{power_keyword} {command} param2"]
    
    def get_fallback_predictions(self, query: str = "") -> List[str]:
        """
        Get fallback predictions when model generation fails.
        """
        if query:
            common_queries = [
                f"{query} from employees where",
                f"{query} all employees in",
                f"{query} the total number of"
            ]
            return common_queries
        else:
            # Default fallback if no query is provided
            return [
                "select the total number of employees",
                "find all sales data for last quarter",
                "list customers with highest orders"
            ]

    def get_similar_queries(self, query: str, qa_service) -> List[str]:
        """
        Get similar queries from the vector database.
        """
        result = qa_service.get_similar_questions(query)
        
        matches = []
        if result['matches']:
            for match in result['matches']:
                if match['score'] > 0.8:
                    matches.append(match['metadata']['question'])
        
        return matches
