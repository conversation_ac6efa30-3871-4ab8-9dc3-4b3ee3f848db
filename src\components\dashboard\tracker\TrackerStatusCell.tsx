
import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

interface TrackerStatusCellProps {
  status: string;
  onStatusChange: (value: string) => void;
}

const TrackerStatusCell: React.FC<TrackerStatusCellProps> = ({ status, onStatusChange }) => {
  return (
    <div className="w-full">
      <Select
        value={status}
        onValueChange={onStatusChange}
      >
        <SelectTrigger 
          className="h-6 text-sm px-1.5 min-w-[90px] font-normal border-none shadow-none bg-transparent focus:ring-0 focus:ring-offset-0" 
          style={{ 
            boxShadow: 'none', 
            outline: 'none',
            paddingRight: '20px'
          }}
        >
          <SelectValue placeholder="Select status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="Open">Open</SelectItem>
          <SelectItem value="InProgress">In Progress</SelectItem>
          <SelectItem value="Closed">Closed</SelectItem>
          <SelectItem value="Cancelled">Cancelled</SelectItem> 
        </SelectContent>
      </Select>
    </div>
  );
};

export default React.memo(TrackerStatusCell);
