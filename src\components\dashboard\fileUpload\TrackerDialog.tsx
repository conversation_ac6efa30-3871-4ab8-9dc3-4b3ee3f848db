
import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch } from '@/hooks/useRedux';
import { addToTracker } from '@/stores/fileSlice';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { uploadToTracker } from '@/services/api/audioTranscript/trackerService';

// Create a custom event for refreshing tracker projects
export const REFRESH_TRACKER_PROJECTS_EVENT = 'refreshTrackerProjects';

interface TrackerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  fileName: string;
  selectedProject?: {
    id: string;
    name: string;
  };
  fileId?: number;
}

const TrackerDialog: React.FC<TrackerDialogProps> = ({ 
  isOpen, 
  onClose, 
  fileName,
  selectedProject,
  fileId
}) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadComplete, setIsUploadComplete] = useState(false);
  const [uploadedProjectName, setUploadedProjectName] = useState<string | null>(null);

  // Reset state when dialog opens/closes
  React.useEffect(() => {
    if (!isOpen) {
      setIsUploadComplete(false);
      setUploadedProjectName(null);
    }
  }, [isOpen]);

  // Separate function for navigation that happens after upload
  const navigateToTracker = useCallback((projectName: string) => {
    navigate(`/transcript/tracker/${encodeURIComponent(projectName)}`);
  }, [navigate]);

  // Handle the upload to tracker
  const handleSaveToTracker = async () => {
    if (!selectedProject) {
      toast.error("Please select a project in the file selector first");
      onClose();
      return;
    }

    if (!fileId) {
      toast.error("File ID is missing. Please try again.");
      onClose();
      return;
    }

    setIsLoading(true);
    try {
      console.log("Uploading to tracker:", {
        fileId,
        projectId: selectedProject.id,
        projectName: selectedProject.name
      });
      
      // Call the API to upload to tracker
      const response = await uploadToTracker({
        fileId,
        projectId: selectedProject.id,
        projectName: selectedProject.name
      });
      
      // Add to tracker in Redux store
      dispatch(addToTracker({
        projectId: selectedProject.id,
        projectName: selectedProject.name,
        fileName
      }));
      
      toast.success(response.message || "Successfully added to tracker");
      
      // Mark upload as complete and store project name for navigation
      setIsUploadComplete(true);
      setUploadedProjectName(selectedProject.name);
      
      // Make sure we're dispatching the event properly
      console.log("Dispatching refresh tracker projects event");
      window.dispatchEvent(new CustomEvent(REFRESH_TRACKER_PROJECTS_EVENT));
    } catch (error) {
      console.error('Error saving to tracker:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to upload to tracker');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle the view tracker button click
  const handleViewTracker = useCallback(() => {
    if (uploadedProjectName) {
      onClose();
      navigateToTracker(uploadedProjectName);
    }
  }, [uploadedProjectName, onClose, navigateToTracker]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>{isUploadComplete ? "Upload Complete" : "Add to Tracker"}</DialogTitle>
        </DialogHeader>
        
        {!isUploadComplete ? (
          <>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">
                  Project Name:
                </Label>
                <div className="col-span-3 border rounded-md p-2 bg-gray-100">
                  {selectedProject ? selectedProject.name : "No project selected"}
                </div>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={onClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button 
                onClick={handleSaveToTracker}
                variant='greenmind' 
                disabled={isLoading || !selectedProject}
              >
                {isLoading ? 'Saving...' : 'Save to Tracker'}
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <div className="py-4 text-center">
              <p className="text-green-600 mb-4">Successfully added to tracker!</p>
              <p>Would you like to view the tracker now?</p>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              <Button 
                onClick={handleViewTracker}
                variant='greenmind'
              >
                View Tracker
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default TrackerDialog;
