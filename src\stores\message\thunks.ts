
import { createAsyncThunk } from '@reduxjs/toolkit';
import { fetchMessages, queryService } from '@/services/api';
import { toast } from 'sonner';
import { Message } from './types';

// Flag to prevent duplicate success toasts
let successToastShown = false;

export const loadMessages = createAsyncThunk(
  'message/loadMessages',
  async (dashboardType: number) => {
    // Only fetch messages for dashboardType 1
    if (dashboardType === 1) {
      const fetchedMessages = await fetchMessages();
      // Ensure all messages have the minimized property set
      return fetchedMessages.map((message: any): Message => ({
        ...message,
        minimized: message.minimized !== undefined ? message.minimized : false,
      }));
    }
    // Return empty array for other dashboard types
    return [];
  }
);

export const submitQuery = createAsyncThunk(
  'message/submitQuery',
  async ({ inputValue, dashboardType }: { inputValue: string; dashboardType: number }) => {
    if (dashboardType === 3) {
      console.log(`Making API call for query: ${inputValue}`);
      try {
        const queryResult = await queryService.regenerateQuery(inputValue);
        
        // Only show success toast if we haven't shown one yet
        if (!successToastShown) {
          toast.success('Query Successful', {
            description: 'Data retrieved successfully',
            style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
          });
          
          successToastShown = true;
          
          // Reset success toast flag after 3 seconds
          setTimeout(() => {
            successToastShown = false;
          }, 3000);
        }
        
        return { queryResult, inputValue };
      } catch (error) {
        console.error('Error processing query:', error);
        throw error;
      }
    } else {
      // For dashboardType 1 and 2, we'll use mock responses
      return { mockResponse: true, inputValue };
    }
  }
);

export const regenerateQueryAction = createAsyncThunk(
  'message/regenerateQuery',
  async (originalQuery: string) => {
    console.log(`Regenerating query: ${originalQuery}`);
    try {
      const queryResult = await queryService.regenerateQuery(originalQuery);
      return { queryResult, originalQuery };
    } catch (error) {
      console.error('Error regenerating query:', error);
      throw error;
    }
  }
);
