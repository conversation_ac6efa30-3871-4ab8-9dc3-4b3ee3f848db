import React, { useState, useCallback, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import TrackerHeader from './TrackerHeader';
import TrackerTable from './TrackerTable';
import TrackerTableActions from './TrackerTableActions';
import TrackerCommentModal from './TrackerCommentModal';
import { useTrackerItems } from '@/hooks/useTrackerItems';
import { useTrackerComments } from '@/hooks/useTrackerComments';
import { updateTrackerItemsBatch } from '@/services/api/audioTranscript/trackerService';
import { SuccessDialog } from '@/components/ui/success-dialog';
import { useNavigationEvents } from '@/hooks/useNavigationEvents';
import { toast } from 'sonner';

// Main TrackerTableView Component
const TrackerTableView: React.FC = () => {
  const { projectName } = useParams<{ projectName: string }>();
  const navigate = useNavigate();
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [successTitle, setSuccessTitle] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [selectedTranscriptIds, setSelectedTranscriptIds] = useState<string[]>([]);
  const [showAssignedToMe, setShowAssignedToMe] = useState(false);

  const {
    trackerItems,
    setTrackerItems,
    transcriptOptions,
    isLoading,
    displayProjectName,
    transcripts,
    handleInputChange,
    handleStatusChange,
    addComment,
    getModifiedItems,
    modifiedItemIds,
    setModifiedItemIds,
    reloadTrackerItems
  } = useTrackerItems(projectName);

  const {
    activeCommentId,
    setActiveCommentId,
    newCommentText,
    setNewCommentText,
    handleAddComment,
    userInitials
  } = useTrackerComments(addComment);

  // Track unsaved changes
  useEffect(() => {
    setHasUnsavedChanges(modifiedItemIds.size > 0);
  }, [modifiedItemIds]);

  // Set up navigation event handling
  useNavigationEvents(
    // Before navigate - can show confirmation if needed
    () => {
      if (hasUnsavedChanges) {
        // Just log for now, we could add a confirmation dialog later
        console.warn('Navigating away with unsaved changes');
      }
    },
    // After navigate - can refresh data if needed
    (newPath) => {
      // If returning to this view with a different project, we need to reset state
      if (newPath.includes('/transcript/tracker/')) {
        console.log('Returned to tracker view, refreshing data');
        reloadTrackerItems();
      }
    }
  );

  const toggleRowExpansion = useCallback((id: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  }, []);

  const handleSave = useCallback(async () => {
    try {
      // Get only the modified items
      const modifiedItems = getModifiedItems();
      
      // If no items were modified, show a message and return
      if (modifiedItems.length === 0) {
        setSuccessTitle('No Changes');
        setSuccessMessage('No changes to save');
        setShowSuccessDialog(true);
        return;
      }
      
      // Show loading state
      setIsSaving(true);
      console.log('Saving modified items:', modifiedItems);
      
      // Call the API to update only modified tracker items
      const response = await updateTrackerItemsBatch(modifiedItems);
      
      // Set success message from API response
      setSuccessTitle('Success');
      setSuccessMessage(response.message || `Successfully saved ${modifiedItems.length} item(s)`);
      setShowSuccessDialog(true);
      
      // Clear the modified items tracking after successful save
      setModifiedItemIds(new Set());
    } catch (error) {
      console.error('Error saving tracker items:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save changes');
    } finally {
      setIsSaving(false);
    }
  }, [getModifiedItems, setModifiedItemIds]);

  const handleCancel = useCallback(() => {
    // If there are unsaved changes, confirm before navigating
    if (hasUnsavedChanges) {
      const confirmed = window.confirm('You have unsaved changes. Are you sure you want to discard them?');
      if (!confirmed) return;
    }
    navigate('/transcript');
  }, [navigate, hasUnsavedChanges]);

  // Add a function to clear the newly added row
  const handleClearNewRow = useCallback((rowId: string) => {
    // Remove the row from tracker items
    setTrackerItems(prevItems => prevItems.filter(item => item.id !== rowId));
    
    // Remove from modified items if it was there
    setModifiedItemIds(prev => {
      const newSet = new Set(prev);
      newSet.delete(rowId);
      return newSet;
    });
    
    // Remove from expanded rows if it was expanded
    setExpandedRows(prev => {
      const newExpandedRows = { ...prev };
      delete newExpandedRows[rowId];
      return newExpandedRows;
    });
  }, []);

  // Update the handleAddNewRow function to return the new row ID
  const handleAddNewRow = useCallback(() => {
    // Create a new empty tracker item with a timestamp-based ID
    const newId = `new-${Date.now()}`;
    
    const newItem = {
      id: newId,
      projectName: displayProjectName || '',
      activity: '',
      activityType: 'ActionItem', // Default type
      assignedOn: new Date().toISOString().split('T')[0], // Today's date
      activityStatus: 'Open', // Default status
      assignedTo: '',
      eta: '',
      meetingId: '',
      comments: '',
      commentItems: []
    };
    
    // Add the new item to the tracker items
    setTrackerItems(prevItems => [newItem, ...prevItems]);
    
    // Mark this item as modified so it will be saved
    setModifiedItemIds(prev => new Set(prev).add(newId));
    
    // Optionally, expand the new row
    setExpandedRows(prev => ({
      ...prev,
      [newId]: true
    }));
    
    // Return the new ID so the TrackerTable component can track it
    return newId;
  }, [displayProjectName]);

  const handleTranscriptSelect = (value: string | string[]) => {
    const transcriptIds = Array.isArray(value) ? value : [value];
    setSelectedTranscriptIds(transcriptIds);
    console.log("Selected transcripts:", transcriptIds);
    // Add logic to filter tracker items based on selected transcripts
  };

  const handleShowAssignedToMeChange = (checked: boolean) => {
    setShowAssignedToMe(checked);
    console.log("Show assigned to me:", checked);
    // Add logic to filter tracker items based on assignment
  };

  const handleShare = () => {
    // Implement share functionality
    const shareUrl = `${window.location.origin}/tracker?project=${displayProjectName}&transcripts=${selectedTranscriptIds.join(',')}&assignedToMe=${showAssignedToMe}`;
    
    // Copy to clipboard
    navigator.clipboard.writeText(shareUrl)
      .then(() => toast.success("Share link copied to clipboard"))
      .catch(() => toast.error("Failed to copy share link"));
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
        <span className="ml-2 text-gray-600">Loading tracker data...</span>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="flex-shrink-0 border-b border-gray-200 bg-white px-4">
        <TrackerHeader 
          projectName={displayProjectName || ''} 
          transcriptOptions={transcriptOptions || []}
          onTranscriptSelect={handleTranscriptSelect}
          onShowAssignedToMeChange={handleShowAssignedToMeChange}
          onShare={handleShare}
        />
      </div>

      {/* This div is critical for proper scrolling */}
      <div className="flex-1 px-4" style={{ overflow: 'hidden' }}>
        <TrackerTable 
          trackerItems={trackerItems}
          expandedRows={expandedRows}
          handleInputChange={handleInputChange}
          handleStatusChange={handleStatusChange}
          toggleRowExpansion={toggleRowExpansion}
          setActiveCommentId={setActiveCommentId}
          onAddNewRow={handleAddNewRow}
          onClearRow={handleClearNewRow}
        />
      </div>

      <div className="flex-shrink-0 border-t border-gray-200 bg-gray-50 px-4">
        <TrackerTableActions
          onSave={handleSave}
          onCancel={handleCancel}
          isSaving={isSaving}
          hasChanges={hasUnsavedChanges}
        />
      </div>

      <TrackerCommentModal
        isOpen={activeCommentId !== null}
        onClose={() => setActiveCommentId(null)}
        commentItems={activeCommentId ? 
          trackerItems.find(item => item.id === activeCommentId)?.commentItems : []}
        onAddComment={handleAddComment}
        newCommentText={newCommentText}
        setNewCommentText={setNewCommentText}
        userInitials={userInitials}
      />

      <SuccessDialog
        open={showSuccessDialog}
        onOpenChange={setShowSuccessDialog}
        onClose={() => setShowSuccessDialog(false)}
        title={successTitle}
        message={successMessage}
      />
    </div>
  );
};

export default TrackerTableView;
