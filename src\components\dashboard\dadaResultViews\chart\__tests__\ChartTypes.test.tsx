
import { render, screen } from '@testing-library/react';
import { VerticalBarChart, HorizontalBarChart } from '../ChartTypes';
import { describe, it, expect, vi } from 'vitest';

// Mock recharts components
vi.mock('recharts', () => {
  const OriginalModule = vi.importActual('recharts');
  return {
    ...OriginalModule,
    ResponsiveContainer: ({ children }: { children: React.ReactNode }) => <div data-testid="responsive-container">{children}</div>,
    BarChart: ({ children }: { children: React.ReactNode }) => <div data-testid="bar-chart">{children}</div>,
    Bar: ({ children }: { children: React.ReactNode }) => <div data-testid="bar">{children}</div>,
    XAxis: () => <div data-testid="xaxis"></div>,
    YAxis: () => <div data-testid="yaxis"></div>,
    CartesianGrid: () => <div data-testid="cartesian-grid"></div>,
    Tooltip: () => <div data-testid="tooltip"></div>,
    Legend: () => <div data-testid="legend"></div>,
    Cell: () => <div data-testid="cell"></div>,
    LabelList: () => <div data-testid="label-list"></div>
  };
});

// Mock custom bar components
vi.mock('../CustomBarShape', () => ({
  RoundedBar: () => <div data-testid="rounded-bar"></div>,
  HorizontalRoundedBar: () => <div data-testid="horizontal-rounded-bar"></div>
}));

describe('VerticalBarChart', () => {
  const mockProps = {
    chartData: [{ category: 'A', value: 10 }, { category: 'B', value: 20 }],
    categoryAxis: 'category',
    dataColumns: ['value'],
    queryType: 'count',
    maxValue: 20
  };

  it('renders correctly with data', () => {
    render(<VerticalBarChart {...mockProps} />);
    
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    expect(screen.getByTestId('cartesian-grid')).toBeInTheDocument();
    expect(screen.getByTestId('xaxis')).toBeInTheDocument();
    expect(screen.getByTestId('yaxis')).toBeInTheDocument();
    expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    expect(screen.getByTestId('legend')).toBeInTheDocument();
    expect(screen.getByTestId('bar')).toBeInTheDocument();
  });
});

describe('HorizontalBarChart', () => {
  const mockProps = {
    chartData: [{ category: 'A', value: 10 }, { category: 'B', value: 20 }],
    categoryAxis: 'category',
    dataColumns: ['value'],
    queryType: 'count',
    maxValue: 20
  };

  it('renders correctly with data', () => {
    render(<HorizontalBarChart {...mockProps} />);
    
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    expect(screen.getByTestId('cartesian-grid')).toBeInTheDocument();
    expect(screen.getByTestId('xaxis')).toBeInTheDocument();
    expect(screen.getByTestId('yaxis')).toBeInTheDocument();
    expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    expect(screen.getByTestId('legend')).toBeInTheDocument();
    expect(screen.getByTestId('bar')).toBeInTheDocument();
  });
});
