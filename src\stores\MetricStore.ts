
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Step } from './metricSlice';

// Replace mobx implementation with Redux implementation
export class MetricStore {
  currentStep: Step = "define";
  publishCompleted: boolean = false;
  
  constructor() {
    // Initialize with default values
  }

  setCurrentStep(step: Step) {
    this.currentStep = step;
  }

  setPublishCompleted(completed: boolean) {
    this.publishCompleted = completed;
  }

  getCurrentStep() {
    return this.currentStep;
  }

  getPublishCompleted() {
    return this.publishCompleted;
  }

  resetState() {
    this.currentStep = "define";
    this.publishCompleted = false;
  }
}

export default MetricStore;
