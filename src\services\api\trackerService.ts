import { toast } from 'sonner';
import { API_KEY, baseUrl } from './audioTranscript/config';

interface UploadToTrackerParams {
  fileId: number;
  projectId: string;
  projectName: string;
}

interface TrackerResponse {
  success: boolean;
  message: string;
}

/**
 * Uploads a file to the tracker for a specific project
 * @param params Object containing fileId, projectId and projectName
 * @returns Promise with the response from the API
 */
export const uploadToTracker = async (
  params: UploadToTrackerParams
): Promise<TrackerResponse> => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }

    // Encode the project name for the URL
    const encodedProjectName = encodeURIComponent(params.projectName);
    
    // Construct the URL with query parameters
    const url = `${baseUrl}/tracker/${params.fileId}?project_name=${encodedProjectName}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Failed to upload to tracker (${response.status})`);
    }

    const data = await response.json();
    return data as TrackerResponse;
  } catch (error) {
    console.error('Error uploading to tracker:', error);
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    toast.error('Failed to upload to tracker', {
      description: errorMessage
    });
    throw error;
  }
};

/**
 * Gets all files for a specific project in the tracker
 * @param projectId The ID of the project
 * @returns Promise with the tracker files
 */
export const getTrackerFiles = async (projectId: string) => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }

    const response = await fetch(`${baseUrl}/tracker/${projectId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': API_KEY || '',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Failed to get tracker files (${response.status})`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting tracker files:', error);
    throw error;
  }
};