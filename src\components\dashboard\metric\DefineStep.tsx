
import React from "react"
import { MetricFormData } from "@/stores/metricSlice"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

interface DefineStepProps {
  formData: MetricFormData
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
}

export const DefineStep: React.FC<DefineStepProps> = ({ formData, handleInputChange }) => {
  return (
    <div className="space-y-6">
      <div>
        <label htmlFor="metricName" className="block text-sm font-medium text-gray-700 mb-1">
          Metric Name:
        </label>
        <Input
          type="text"
          id="metricName"
          name="metricName"
          value={formData.metricName}
          onChange={handleInputChange}
          className="w-full"
        />
      </div>

      <div>
        <label htmlFor="metricDescription" className="block text-sm font-medium text-gray-700 mb-1">
          Metric Description:
        </label>
        <Textarea
          id="metricDescription"
          name="metricDescription"
          value={formData.metricDescription}
          onChange={handleInputChange}
          rows={4}
          className="w-full"
        />
      </div>

      <div>
        <label htmlFor="metricFormula" className="block text-sm font-medium text-gray-700 mb-1">
          Metric Formula:
        </label>
        <Input
          type="text"
          id="metricFormula"
          name="metricFormula"
          value={formData.metricFormula}
          onChange={handleInputChange}
          className="w-full"
        />
      </div>

      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Required Column:</h3>
        <div className="border border-gray-300 rounded overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr className="bg-lime-300">
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  ColumnName
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  DataType
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr className="bg-white">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Value 1</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Value 2</td>
              </tr>
              <tr className="bg-lime-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Value 4</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Value 5</td>
              </tr>
              <tr className="bg-white">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Value 7</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Value 8</td>
              </tr>
              <tr className="bg-lime-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Value 10</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Value 11</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
