import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { createProject, updateProject } from '@/services/api/admin/projectService';
import { getProjectGroups, createProjectGroup } from '@/services/api/admin/projectGroupService';
import { toast } from 'sonner';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ArrowLeft, MoreHorizontal } from 'lucide-react';

interface ProjectFormProps {
  onSave: (project: any) => void;
  onCancel: () => void;
  initialData?: any;
}

// Project Group Dialog Component
const ProjectGroupDialog = ({ onSelect }: { onSelect: (group: string) => void }) => {
  const [newGroup, setNewGroup] = useState('');
  const [groups, setGroups] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  // Fetch project groups when dialog opens
  useEffect(() => {
    fetchProjectGroups();
  }, []);
  
  const fetchProjectGroups = async () => {
    try {
      setIsLoading(true);
      const groupNames = await getProjectGroups();
      setGroups(groupNames);
    } catch (error) {
      console.error('Error fetching project groups:', error);
      toast.error('Failed to load project groups');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleAddGroup = async () => {
    if (newGroup.trim()) {
      try {
        setIsLoading(true);
        
        // Call the API to create a new project group
        await createProjectGroup(newGroup);
        
        // Refresh the list of project groups
        await fetchProjectGroups();
        
        // Clear the input field
        setNewGroup('');
        
        toast.success('Project group created successfully');
      } catch (error) {
        console.error('Error creating project group:', error);
        toast.error('Failed to create project group');
      } finally {
        setIsLoading(false);
      }
    }
  };
  
  const handleSelect = (group: string) => {
    onSelect(group);
  };
  
  return (
    <DialogContent className="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Project Group</DialogTitle>
      </DialogHeader>
      <div className="space-y-4 py-2">
        <div className="flex items-center space-x-2">
          <Input 
            placeholder="Enter new project group" 
            value={newGroup} 
            onChange={(e) => setNewGroup(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleAddGroup();
              }
            }}
          />
          <Button size="sm" onClick={handleAddGroup} variant="white">New</Button>
        </div>
        <div className="border rounded-md max-h-32 overflow-y-auto custom-scrollbar">
          {groups.map((group, index) => (
            <div 
              key={index} 
              className="p-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSelect(group)}
            >
              {group}
            </div>
          ))}
        </div>
      </div>
      <div className="flex justify-end space-x-2 mt-4">
        <Button variant="outline" onClick={() => onSelect('')}>Cancel</Button>
        <Button variant="greenmind" onClick={() => handleSelect(groups[0])}>Save</Button>
      </div>
    </DialogContent>
  );
};

const ProjectForm: React.FC<ProjectFormProps> = ({ onSave, onCancel, initialData }) => {
  const [project, setProject] = useState({
    id: initialData?.project_id?.toString() || '',
    name: initialData?.project_name || '',
    description: initialData?.project_description || '',
    projectGroup: initialData?.project_group || '',
    channel: initialData?.collaboration_channel?.split(':')[0] || 'Select',
    channelName: initialData?.channel_name || '',
    owner: initialData?.project_owner || '',
    teamMembers: Array.isArray(initialData?.team_members) 
      ? initialData.team_members.join('\n') 
      : initialData?.team_members || '',
    project_stakeholders: Array.isArray(initialData?.project_stakeholders) 
      ? initialData.project_stakeholders.join('\n') 
      : initialData?.project_stakeholders || '',
    status: initialData?.project_status || 'Select',
  });

  const [errors, setErrors] = useState({
    name: '',
    description: '',
    projectGroup: '',
    channel: '',
    channelName: '',
    owner: '',
    status: '',
  });

  const [touched, setTouched] = useState({
    name: false,
    description: false,
    projectGroup: false,
    channel: false,
    channelName: false,
    owner: false,
    status: false,
  });

  const [isFormValid, setIsFormValid] = useState(false);
  const [attemptedSubmit, setAttemptedSubmit] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Handle project group selection from dialog
  const handleProjectGroupSelect = (group: string) => {
    // Close the dialog regardless of selection
    setIsDialogOpen(false);
    
    // Only update project if a group was selected
    if (group) {
      setProject(prev => ({ ...prev, projectGroup: group }));
      setTouched(prev => ({ ...prev, projectGroup: true }));
      setErrors(prev => ({ ...prev, projectGroup: validateField('projectGroup', group) }));
    }
  };

  // Add projectGroup to validateField function
  const validateField = (name: string, value: string) => {
    switch (name) {
      case 'name':
        return value.trim() === '' ? 'Project name is required' : '';
      case 'description':
        return value.trim() === '' ? 'Project description is required' : '';
      case 'projectGroup':
        return value.trim() === '' ? 'Project group is required' : '';
      case 'channel':
        return value === 'Select' ? 'Please select a collaboration channel' : '';
      case 'channelName':
        return value.trim() === '' ? 'Channel name is required' : '';
      case 'owner':
        return value.trim() === '' ? 'Project owner is required' : '';
      case 'status':
        return value === 'Select' ? 'Please select a project status' : '';
      default:
        return '';
    }
  };

  // Update validateForm to include projectGroup
  const validateForm = () => {
    const newErrors = {
      name: validateField('name', project.name),
      description: validateField('description', project.description),
      projectGroup: validateField('projectGroup', project.projectGroup),
      channel: validateField('channel', project.channel),
      channelName: validateField('channelName', project.channelName),
      owner: validateField('owner', project.owner),
      status: validateField('status', project.status),
    };

    setErrors(newErrors);
    
    // Form is valid if there are no error messages
    return !Object.values(newErrors).some(error => error !== '');
  };

  // Check if all required fields are filled
  useEffect(() => {
    const isValid = validateForm();
    setIsFormValid(isValid);
    
    // If user has attempted to submit, show all errors
    if (attemptedSubmit) {
      validateForm();
    }
  }, [project, attemptedSubmit]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProject(prev => ({ ...prev, [name]: value }));
    
    // Mark field as touched
    setTouched(prev => ({ ...prev, [name]: true }));
    
    // If field is touched or user attempted submit, validate it
    if (touched[name as keyof typeof touched] || attemptedSubmit) {
      setErrors(prev => ({ ...prev, [name]: validateField(name, value) }));
    }
  };

  const handleStatusChange = (value: string) => {
    setProject(prev => ({ ...prev, status: value }));
    setTouched(prev => ({ ...prev, status: true }));
    
    if (touched.status || attemptedSubmit) {
      setErrors(prev => ({ ...prev, status: validateField('status', value) }));
    }
  };

  const handleChannelChange = (value: string) => {
    setProject(prev => ({ ...prev, channel: value }));
    setTouched(prev => ({ ...prev, channel: true }));
    
    if (touched.channel || attemptedSubmit) {
      setErrors(prev => ({ ...prev, channel: validateField('channel', value) }));
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    setErrors(prev => ({ ...prev, [name]: validateField(name, project[name as keyof typeof project] as string) }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setAttemptedSubmit(true);
    
    const isValid = validateForm();
    if (!isValid) {
      toast.error('Please fix the errors in the form');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Prepare data for API
      const apiProjectData = {
        project_name: project.name,
        project_description: project.description,
        project_group: project.projectGroup,
        collaboration_channel: `${project.channel}:${project.channelName}`,
        channel_name: project.channelName,
        project_owner: project.owner,
        team_members: project.teamMembers.split('\n').filter(member => member.trim() !== ''),
        project_stakeholders: project.project_stakeholders.split('\n').filter(stakeholder => stakeholder.trim() !== ''),
        project_status: project.status
      };
      
      let savedProject;
      
      if (project.id) {
        // Update existing project
        savedProject = await updateProject({
          ...apiProjectData,
          project_id: parseInt(project.id)
        });
        toast.success('Project updated successfully');
      } else {
        // Create new project - don't include project_id as it's auto-generated
        savedProject = await createProject(apiProjectData);
        toast.success('Project created successfully');
      }
      
      // Map the API response back to our form format
      const formattedProject = {
        id: savedProject.project_id?.toString() || '',
        name: savedProject.project_name,
        description: savedProject.project_description,
        projectGroup: savedProject.project_group,
        channel: (savedProject.collaboration_channel as string).split(':')[0],
        channelName: savedProject.channel_name,
        owner: savedProject.project_owner,
        teamMembers: Array.isArray(savedProject.team_members) 
          ? savedProject.team_members.join('\n') 
          : String(savedProject.team_members),
        project_stakeholders: Array.isArray(savedProject.project_stakeholders) 
          ? savedProject.project_stakeholders.join('\n') 
          : String(savedProject.project_stakeholders),
        status: savedProject.project_status
      };
      
      onSave(formattedProject);
    } catch (error) {
      console.error('Error saving project:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save project');
    } finally {
      setIsSubmitting(false);
    }
  };

  const isEditMode = !!initialData?.project_id;

  return (
    <div className="border rounded-md bg-white p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Back button for better navigation */}
        <div className="flex items-center mb-4">
          <Button 
            type="button" 
            variant="ghost" 
            onClick={onCancel} 
            className="p-0 mr-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h2 className="text-lg font-medium">
            {isEditMode ? 'Edit Project' : 'Create New Project'}
          </h2>
        </div>
        
        {/* Project ID field (only in edit mode) */}
        {isEditMode && (
          <div className="space-y-2">
            <Label htmlFor="id">Project ID</Label>
            <Input
              id="id"
              name="id"
              value={project.id}
              onChange={handleChange}
              placeholder="123"
              disabled
              className="bg-gray-50"
            />
          </div>
        )}

        {/* Project Name field */}
        <div className="space-y-2">
          <Label htmlFor="name">Project Name</Label>
          <Input
            id="name"
            name="name"
            value={project.name}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Enter project name"
            className={errors.name && (touched.name || attemptedSubmit) ? "border-red-500" : ""}
          />
          {errors.name && (touched.name || attemptedSubmit) && (
            <p className="text-red-500 text-sm">{errors.name}</p>
          )}
        </div>

        {/* Project Description field */}
        <div className="space-y-2">
          <Label htmlFor="description">Project Description</Label>
          <Textarea
            id="description"
            name="description"
            value={project.description}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Describe the project purpose and goals"
            className={errors.description && (touched.description || attemptedSubmit) ? "border-red-500" : ""}
          />
          {errors.description && (touched.description || attemptedSubmit) && (
            <p className="text-red-500 text-sm">{errors.description}</p>
          )}
        </div>

        {/* Project Group field */}
        <div className="space-y-2">
          <Label htmlFor="projectGroup">Project Group</Label>
          <div className="flex items-center space-x-2">
            <Input
              id="projectGroup"
              name="projectGroup"
              value={project.projectGroup}
              readOnly
              placeholder="Click ... to select a project group"
              className={`${errors.projectGroup && (touched.projectGroup || attemptedSubmit) ? "border-red-500" : ""} flex-1 bg-gray-50`}
            />
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <ProjectGroupDialog onSelect={handleProjectGroupSelect} />
            </Dialog>
          </div>
          {errors.projectGroup && (touched.projectGroup || attemptedSubmit) && (
            <p className="text-red-500 text-sm">{errors.projectGroup}</p>
          )}
        </div>

        {/* Collaboration Channel field */}
        <div className="space-y-2">
          <Label htmlFor="channel">Collaboration Channel</Label>
          <div className="flex gap-4">
            <div className="w-1/3">
              <Select 
                value={project.channel} 
                onValueChange={handleChannelChange}
              >
                <SelectTrigger id="channel" className={errors.channel && (touched.channel || attemptedSubmit) ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Select">Select</SelectItem>
                  <SelectItem value="Slack">Slack</SelectItem>
                  <SelectItem value="Teams">Teams</SelectItem>
                </SelectContent>
              </Select>
              {errors.channel && (touched.channel || attemptedSubmit) && (
                <p className="text-red-500 text-sm">{errors.channel}</p>
              )}
            </div>
            <div className="flex-1">
              <Input
                id="channelName"
                name="channelName"
                value={project.channelName}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="#Project Collaboration"
                className={errors.channelName && (touched.channelName || attemptedSubmit) ? "border-red-500" : ""}
              />
              {errors.channelName && (touched.channelName || attemptedSubmit) && (
                <p className="text-red-500 text-sm">{errors.channelName}</p>
              )}
            </div>
          </div>
          <p className="text-sm text-gray-500">Select a platform and enter the channel name</p>
        </div>

        {/* Project Owner field */}
        <div className="space-y-2">
          <Label htmlFor="owner">Project Owner</Label>
          <Input
            id="owner"
            name="owner"
            value={project.owner}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="<EMAIL>"
            className={errors.owner && (touched.owner || attemptedSubmit) ? "border-red-500" : ""}
          />
          {errors.owner && (touched.owner || attemptedSubmit) && (
            <p className="text-red-500 text-sm">{errors.owner}</p>
          )}
        </div>

        {/* Team Members field */}
        <div className="space-y-2">
          <Label htmlFor="teamMembers">Team Members</Label>
          <Textarea
            id="teamMembers"
            name="teamMembers"
            value={project.teamMembers}
            onChange={handleChange}
            placeholder="Enter team member emails (one per line)"
            rows={4}
            className="resize-y"
          />
        </div>

        {/* Project Stakeholders field */}
        <div className="space-y-2">
          <Label htmlFor="project_stakeholders">Project Stakeholders</Label>
          <Textarea
            id="project_stakeholders"
            name="project_stakeholders"
            value={project.project_stakeholders}
            onChange={handleChange}
            placeholder="Enter stakeholder names or emails (one per line)"
            rows={4}
            className="resize-y"
          />
          <p className="text-sm text-gray-500">Enter one stakeholder per line</p>
        </div>

        {/* Project Status field */}
        <div className="space-y-2">
          <Label htmlFor="status">Project Status</Label>
          <Select value={project.status} onValueChange={handleStatusChange}>
            <SelectTrigger className={errors.status && (touched.status || attemptedSubmit) ? "border-red-500" : ""}>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Select">Select</SelectItem>
              <SelectItem value="Active">Active</SelectItem>
              <SelectItem value="Closed">Closed</SelectItem>
            </SelectContent>
          </Select>
          {errors.status && (touched.status || attemptedSubmit) && (
            <p className="text-red-500 text-sm">{errors.status}</p>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex justify-end space-x-4 pt-4 border-t">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel} 
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={isSubmitting}
            variant='greenmind'
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ProjectForm;

