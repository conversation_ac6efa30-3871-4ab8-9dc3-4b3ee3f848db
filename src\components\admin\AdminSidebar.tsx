
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Folder, FolderOpen, ChevronDown, ChevronUp, Search, PanelLeftClose, PanelLeftOpen, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { getProjectNames, ProjectListItem } from '@/services/api/admin/projectService';
import { toast } from 'sonner';
import { useAppSelector } from '@/hooks/useRedux';

interface Project {
  id: string;
  name: string;
}

interface AdminSidebarProps {
  collapsed?: boolean;
  onToggle?: () => void;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ collapsed: propCollapsed, onToggle }) => {
  const navigate = useNavigate();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isProjectExpanded, setIsProjectExpanded] = useState(true);
  const [isHovering, setIsHovering] = useState(false);
  
  // Use the prop value if provided, otherwise use internal state
  const effectiveCollapsed = propCollapsed !== undefined ? propCollapsed : isCollapsed;
  
  // Get the refresh trigger from Redux store
  const { refreshTrigger } = useAppSelector(state => state.project);

  // Fetch projects from API
  useEffect(() => {
    fetchProjects();
  }, [refreshTrigger]); // Re-fetch when refreshTrigger changes

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      const projectsData = await getProjectNames();
      
      // Map API response to our component's format
      const formattedProjects = projectsData.map(project => ({
        id: project.project_id.toString(),
        name: project.project_name
      }));
      
      setProjects(formattedProjects);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast.error('Failed to load projects');
    } finally {
      setIsLoading(false);
    }
  };

  const filteredProjects = projects.filter(project => 
    project.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleSidebar = () => {
    if (onToggle) {
      // Use the parent's toggle function if provided
      onToggle();
    } else {
      // Otherwise use internal state
      setIsCollapsed(!isCollapsed);
    }
  };

  const navigateToMenu = () => {
    navigate('/Admin/menu');
  };

  if (effectiveCollapsed) {
    return (
      <div 
        className="w-16 bg-gray-200 border-r border-gray-200 flex flex-col items-center py-4 relative"
      >
        <button 
          onClick={toggleSidebar}
          className="w-10 h-10 rounded-md flex items-center justify-center hover:bg-gray-100"
          aria-label="Expand sidebar"
        >
          <PanelLeftOpen size={20} className="h-5 w-5 text-gray-600" />
        </button>

        <div className="flex flex-col space-y-2 mt-4">
          <button 
            onClick={navigateToMenu}
            className="w-10 h-10 rounded-md flex items-center justify-center hover:bg-gray-100 text-gray-600"
          >
            <Folder size={20} />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="w-64 flex flex-col h-full bg-gray-200 border relative"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div className="p-3">
        <div className="flex items-center space-x-2 group">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="text"
              placeholder="Search files..."
              className="pl-8 h-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <button 
            onClick={toggleSidebar}
            className={`flex-shrink-0 w-8 h-8 rounded-md flex items-center justify-center hover:bg-gray-100 transition-opacity duration-200 ${isHovering ? 'opacity-100' : 'opacity-0'}`}
            aria-label="Collapse sidebar"
          >
            <PanelLeftClose size={20} className="h-5 w-5 text-gray-600 hover:text-gray-900" />
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto py-2 [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden">
        <div className="space-y-1">
          {/* Projects Section */}
          <div className="px-3">
            <div 
              className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
              onClick={() => setIsProjectExpanded(!isProjectExpanded)}
            >
              <div className="flex items-center">
                {isProjectExpanded ? (
                  <FolderOpen className="h-5 w-5 mr-2 text-green-600" />
                ) : (
                  <Folder className="h-5 w-5 mr-2 text-green-600" />
                )}
                <span className="text-sm font-medium">Projects</span>
              </div>
              <div className="flex items-center">
                {isLoading && (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin text-green-600" />
                )}
                {isProjectExpanded ? (
                  <ChevronUp size={16} className={`text-gray-600 ${isHovering ? 'opacity-100' : 'opacity-0'} transition-opacity duration-200`} />
                ) : (
                  <ChevronDown size={16} className={`text-gray-600 ${isHovering ? 'opacity-100' : 'opacity-0'} transition-opacity duration-200`} />
                )}
              </div>
            </div>

            {isProjectExpanded && (
              <div className="ml-7 mt-1">
                {isLoading ? (
                  null
                ) : filteredProjects.length > 0 ? (
                  <div className="space-y-1">
                    {filteredProjects.map(project => (
                      <div 
                        key={project.id}
                        onClick={() => navigate(`/Admin/project/${project.id}`)}
                        className="px-2 py-1 text-sm cursor-pointer text-gray-700 hover:text-blue-600 transition-colors"
                      >
                        <span className="truncate">{project.name}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 p-2">
                    {searchQuery ? 'No matching projects' : 'No projects yet'}
                  </p>
                )}
                
                <div className="mt-2 pt-2 border-t border-gray-300">
                  <div 
                    onClick={navigateToMenu}
                    className="px-2 py-1 text-sm cursor-pointer text-blue-600 font-medium"
                  >
                    Manage Projects
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSidebar;
