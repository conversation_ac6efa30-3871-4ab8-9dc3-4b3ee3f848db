
import React, { useEffect, useState } from 'react';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import TranscriptTabNavigation from '../dashboard/fileUpload/TranscriptTabNavigation';
import TranscriptTabView from '../dashboard/fileUpload/TranscriptTabView';
import { TranscriptTab } from '../dashboard/models';
import { getTranscriptByFileName } from '@/services/api/audioTranscript';
import { TranscriptItem } from '@/components/dashboard/fileUpload/types';

interface SharedTranscriptContentProps {
  fileName: string;
  folderType: 'personal' | 'project';
}

const SharedTranscriptContent: React.FC<SharedTranscriptContentProps> = ({
  fileName,
  folderType
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<TranscriptTab>('transcript');
  const [transcriptData, setTranscriptData] = useState<{
    transcript: TranscriptItem[];
    file_id?: number;
    audioUrl?: string;
    summary: string;
    actionItems: { action: string; dueDate: string; responsible: string }[];
    importantPoints: string[];
    openQuestions: { question: string }[];
  }>({
    transcript: [],
    file_id: undefined,
    audioUrl: undefined,
    summary: '',
    actionItems: [],
    importantPoints: [],
    openQuestions: []
  });

  useEffect(() => {
    const loadTranscript = async () => {
      try {
        const response = await getTranscriptByFileName(fileName, folderType);
        if (response) {
          console.log("Shared transcript data loaded:", response);
          // Ensure we have the audio URL
          if (!response.audioUrl) {
            console.warn("No audio URL in transcript response");
          }
          
          setTranscriptData({
            transcript: Array.isArray(response.transcript) 
              ? response.transcript 
              : [],
            file_id: response.file_id,
            audioUrl: response.audioUrl, // Make sure to include the audio URL
            summary: response.summary.summary,
            actionItems: Array.isArray(response.summary.actionItems)
              ? response.summary.actionItems.map(item => {
                  if (typeof item === 'string') {
                    return { action: item, dueDate: '', responsible: '' };
                  }
                  return item;
                })
              : [],
            importantPoints: response.summary.importantPoints,
            openQuestions: Array.isArray(response.summary.openQuestions)
              ? response.summary.openQuestions.map(q => {
                  if (typeof q === 'string') {
                    return { question: q };
                  }
                  return q;
                })
              : []
          });
        } else {
          toast.error('Transcript not found');
        }
      } catch (error) {
        console.error('Error loading transcript:', error);
        toast.error('Failed to load transcript');
      } finally {
        setIsLoading(false);
      }
    };

    loadTranscript();
  }, [fileName, folderType]);

  const availableTabs = [
    'transcript',
    ...(transcriptData.summary ? ['summary'] : []),
    ...(transcriptData.importantPoints.length > 0 ? ['notes'] : []),
    ...(transcriptData.actionItems.length > 0 ? ['actionItems'] : []),
    ...(transcriptData.openQuestions.length > 0 ? ['openQuestions'] : [])
  ] as TranscriptTab[];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-semibold text-gray-800">{fileName}</h1>
        </div>
        
        <TranscriptTabNavigation 
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          availableTabs={availableTabs}
          fileName={fileName}
          transcriptData={transcriptData}
          isSharedView={true}
        />

        <TranscriptTabView 
          activeTab={activeTab}
          transcriptData={transcriptData}
        />
      </div>
    </div>
  );
};

export default SharedTranscriptContent;
