
import * as React from "react"
import { Column } from "@tanstack/react-table"
import { Filter, FilterX } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface ColumnFilterProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>
  title?: string
  className?: string
}

export function ColumnFilter<TData, TValue>({
  column,
  title,
  className,
}: ColumnFilterProps<TData, TValue>) {
  const [value, setValue] = React.useState<string>(
    (column.getFilterValue() as string) || ""
  )
  const [isOpen, setIsOpen] = React.useState(false)

  React.useEffect(() => {
    setValue((column.getFilterValue() as string) || "")
  }, [column.getFilterValue()])

  const handleFilterChange = (newValue: string) => {
    setValue(newValue)
    column.setFilterValue(newValue || undefined)
  }

  const clearFilter = () => {
    setValue("")
    column.setFilterValue(undefined)
    setIsOpen(false)
  }

  const hasFilter = Boolean(column.getFilterValue())

  return (
    <div className={cn("flex items-center", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-6 w-6 p-0 text-white hover:bg-teal-700",
              hasFilter && "bg-teal-700"
            )}
            title={`Filter ${title || column.id}`}
          >
            <Filter className="h-3 w-3" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-3 w-60" align="start">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm">Filter {title || column.id}</h4>
              {hasFilter && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilter}
                  className="h-6 w-6 p-0"
                >
                  <FilterX className="h-3 w-3" />
                </Button>
              )}
            </div>
            <Input
              placeholder={`Filter ${title || column.id}...`}
              value={value}
              onChange={(e) => handleFilterChange(e.target.value)}
              className="h-8 w-full"
              autoFocus
            />
            {hasFilter && (
              <div className="text-xs text-gray-500">
                Active filter: "{value}"
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
