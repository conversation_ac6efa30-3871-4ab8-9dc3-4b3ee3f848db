
import React, { useState } from 'react';
import { TranscriptItem } from './types';
import { saveTranscriptToFolder } from '@/services/api/audioTranscript';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Folder, FolderOpen, Pencil, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';

interface SaveDialogProps {
  isOpen: boolean;
  onClose: () => void;
  fileName: string;
  transcriptData: {
    transcript: string | TranscriptItem[];
    summary: string;
    actionItems: string[] | { action: string; dueDate: string; responsible: string }[];
    importantPoints: string[]; // Changed from assignments
    openQuestions: string[] | { question: string }[];
    file_id?: number; // Make file_id optional
  };
  onSaveToPersonal: (fileName: string) => void;
  onSaveToProject: (fileName: string) => void;
}

const SaveDialog: React.FC<SaveDialogProps> = ({
  isOpen,
  onClose,
  fileName,
  transcriptData,
  onSaveToPersonal,
  onSaveToProject
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedFileName, setEditedFileName] = useState(fileName);
  const [savingType, setSavingType] = useState<'personal' | 'project' | null>(null);
  const navigate = useNavigate();

  const handleSave = async (folderType: 'personal' | 'project') => {
    setSavingType(folderType);
    try {
      if (!transcriptData.file_id) {
        throw new Error('File ID is missing');
      }

      const success = await saveTranscriptToFolder(
        transcriptData.file_id,
        editedFileName,
        folderType
      );
      
      if (success) {
        if (folderType === 'personal') {
          onSaveToPersonal(editedFileName);
        } else {
          onSaveToProject(editedFileName);
        }
        
        navigate(`/transcript/${folderType}/${editedFileName}`);
        onClose();
      }
    } catch (error) {
      console.error('Error saving file:', error);
      toast.error('Failed to save file');
    } finally {
      setSavingType(null);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Save Transcript</DialogTitle>
          <div className="flex items-center gap-2">
            {isEditing ? (
              <Input
                value={editedFileName}
                onChange={(e) => setEditedFileName(e.target.value)}
                onBlur={() => setIsEditing(false)}
                autoFocus
                className="text-sm text-gray-600"
              />
            ) : (
              <DialogDescription className="flex items-center gap-2 text-sm text-gray-800">
                Choose where to save "{editedFileName}"
                <button
                  onClick={() => setIsEditing(true)}
                  className="p-1 hover:bg-gray-100 rounded-full"
                >
                  <Pencil size={18} className="text-blue-500" />
                </button>
              </DialogDescription>
            )}
          </div>
        </DialogHeader>
        <div className="flex justify-around gap-4 py-6">
          <Button 
            onClick={() => handleSave('personal')} 
            className="flex flex-col items-center h-auto py-6 px-8 bg-gray-200 hover:bg-green-200"
            variant="outline"
            disabled={!!savingType}
          >
            {savingType === 'personal' ? (
              <>
                <Loader2 className="h-12 w-12 mb-2 text-blue-500 animate-spin" />
                <span>Saving...</span>
              </>
            ) : (
              <>
                <Folder className="h-12 w-12 mb-2 text-blue-500" />
                <span>Save to Personal</span>
              </>
            )}
          </Button>
          <Button 
            onClick={() => handleSave('project')} 
            className="flex flex-col items-center h-auto py-6 px-8 bg-gray-200 hover:bg-green-200"
            variant="outline"
            disabled={!!savingType}
          >
            {savingType === 'project' ? (
              <>
                <Loader2 className="h-12 w-12 mb-2 text-green-500 animate-spin" />
                <span>Saving...</span>
              </>
            ) : (
              <>
                <FolderOpen className="h-12 w-12 mb-2 text-green-500" />
                <span>Save to Project</span>
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SaveDialog;
