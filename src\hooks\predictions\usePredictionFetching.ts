
import { useState, useEffect } from 'react';
import { ChainStage } from '@/services/api/predictions/types';
import { 
  getPredictions,
  parsePredictionQuery,
  isInCommandSequence
} from "@/services/api/predictions";

interface UsePredictionFetchingProps {
  inputValue: string;
  isPredictionsEnabled: boolean;
  isPowerModeActive: boolean;
}

export const usePredictionFetching = ({ 
  inputValue, 
  isPredictionsEnabled,
  isPowerModeActive 
}: UsePredictionFetchingProps) => {
  const [predictions, setPredictions] = useState<string[]>([]);
  const [showPredictions, setShowPredictions] = useState(false);
  const [isChainSequence, setIsChainSequence] = useState(false);
  const [chainStage, setChainStage] = useState<ChainStage>('powerKeyword');

  // This effect handles the main prediction fetching
  useEffect(() => {
    // Skip if predictions are disabled for this dashboard type
    if (!isPredictionsEnabled) {
      setPredictions([]);
      setShowPredictions(false);
      return;
    }

    const fetchPredictions = async () => {
      // Only show predictions if the user has typed @
      const shouldShowPower = inputValue.startsWith('@') && isPowerModeActive;
      const shouldShowRegular = !shouldShowPower && !isPowerModeActive && inputValue.trim().length >= 3;
      
      if (inputValue && (shouldShowPower || shouldShowRegular)) {
        try {
          // Parse the input value to extract power keyword, command, and parameter
          const { powerKeyword, command, parameter } = parsePredictionQuery(inputValue);
          
          console.log("Parsed input:", { powerKeyword, command, parameter });
          
          // Determine which stage of the chain we're in
          if (shouldShowPower) {
            if (!command && !parameter) {
              setChainStage('powerKeyword');
              console.log("In power keyword stage");
            } else if (powerKeyword && command && !parameter) {
              setChainStage('command');
              console.log("In command stage");
            } else if (powerKeyword && command && parameter) {
              setChainStage('parameter');
              console.log("In parameter stage");
            } else {
              setChainStage('final');
              console.log("In final stage");
            }
          }
          
          const params = { 
            query: inputValue,
            powerKeyword,
            command,
            parameter
          };

          console.log("Fetching predictions with params:", params);
          // Use direct getPredictions instead of the debounced version to avoid issues
          const result = await getPredictions(params);
          console.log("Prediction results:", result);
          
          // Format predictions to remove @ prefix for display
          const formattedResults = result?.map(item => item) || [];
          
          setPredictions(formattedResults);
          setShowPredictions(formattedResults && formattedResults.length > 0);
          
          // Detect if we're in a power keyword chain sequence
          setIsChainSequence(shouldShowPower);
        } catch (error) {
          console.error("Error fetching predictions:", error);
          setPredictions([]);
          setShowPredictions(false);
        }
      } else {
        setPredictions([]);
        setShowPredictions(false);
        setIsChainSequence(false);
      }
    };
    
    // Use a short timeout instead of debounce to avoid any cross-component debounce issues
    const timeoutId = setTimeout(fetchPredictions, 100);
    
    return () => {
      clearTimeout(timeoutId);
    };
  }, [inputValue, isPredictionsEnabled, isPowerModeActive]);

  return {
    predictions,
    showPredictions,
    setShowPredictions,
    isChainSequence,
    chainStage,
    setChainStage
  };
};
