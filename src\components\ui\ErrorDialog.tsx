import React from 'react';
import { Cable } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { clearError } from '@/stores/errorSlice';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

const ErrorDialog: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isVisible, message, statusCode } = useAppSelector((state) => state.error);

  return (
    <Dialog open={isVisible} onOpenChange={() => dispatch(clearError())}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-red-700">API Server Connection Failed</DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col items-center p-6">
          <div className="mb-4">
            <Cable size={48} className="text-red-500 bg-red-100 p-2 rounded-full" />
          </div>
          
          <p className="text-center text-black-800 mb-2">{message}</p>
          {statusCode && (
            <p className="text-sm text-red-500">Error Code: {statusCode}</p>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ErrorDialog;
