import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center rounded-sm justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-cursor-not-allowed disabled:opacity-80",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-gray-500 text-white hover:bg-gray-600 cursor-not-allowed",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        // Add custom colors
        blue: "bg-blue-600 text-white hover:bg-blue-700",
        purple: "bg-mindlabz-purple text-white hover:bg-mindlabz-purple/90",
        indigo: "bg-mindlabz-indigo text-white hover:bg-mindlabz-indigo/90",
        white: "bg-white text-black hover:bg-gray-100 border border-gray-400", // New cancel variant
        gray: "bg-gray-500 text-white hover:bg-gray-600",
        greenmind:"bg-[#008282] text-white hover:bg-[#008282]/90",
      },
      size: {
        default: "h-8 px-4 py-1", // Decreased height from h-10
        sm: "h-7 px-3", // Decreased height from h-9
        lg: "h-9 px-8", // Decreased height from h-11
        icon: "h-8 w-8", // Decreased height from h-10
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
