
import { TranscriptItem } from '@/components/dashboard/fileUpload/types';
import { API_KEY, baseUrl } from './config';

export type FolderType = 'personal' | 'project';

export const exportTranscriptToPPT = async (
  fileName: string,
  folderType: FolderType,
  transcriptData: {
    transcript: TranscriptItem[];
    summary: string;
    actionItems: { action: string; dueDate: string; responsible: string }[];
    importantPoints: string[];
    openQuestions: { question: string }[];
  }
): Promise<Blob> => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }

    // Use the original filename with proper encoding
    const encodedFileName = encodeURIComponent(fileName);
    console.log('Request filename:', fileName);
    console.log('Encoded filename:', encodedFileName);

    const url = `${baseUrl}/files/${folderType}/${encodedFileName}/generate-ppt`;
    console.log('Request URL:', url);
    
    // Convert the transcript data to the format expected by the API
    const payload = {
      transcript: transcriptData.transcript.map(item => 
        `${item.speaker}: ${item.text}`).join('\n'),
      summary: transcriptData.summary,
      action_items: transcriptData.actionItems.map(item => item.action),
      important_points: transcriptData.importantPoints,
      open_questions: transcriptData.openQuestions.map(item => item.question)
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'X-API-Key': API_KEY || '',
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Server error response:', errorData);
      throw new Error(errorData.detail || `Failed to generate PowerPoint (${response.status})`);
    }

    const blob = await response.blob();
    if (blob.size === 0) {
      throw new Error('Generated PowerPoint is empty');
    }

    return blob;
  } catch (error) {
    console.error('Error generating PowerPoint:', error);
    throw error;
  }
};

export const exportTranscriptToWord = async (
  fileName: string,
  folderType: FolderType,
  transcriptData: {
    transcript: TranscriptItem[];
    summary: string;
    actionItems: { action: string; dueDate: string; responsible: string }[];
    importantPoints: string[];
    openQuestions: { question: string }[];
  }
): Promise<Blob> => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }

    // Use the original filename with proper encoding
    const encodedFileName = encodeURIComponent(fileName);
    console.log('Request filename:', fileName);
    console.log('Encoded filename:', encodedFileName);

    const url = `${baseUrl}/files/${folderType}/${encodedFileName}/generate-doc`;
    console.log('Request URL:', url);

    // Convert the transcript data to the format expected by the API
    const payload = {
      transcript: transcriptData.transcript.map(item => 
        `${item.speaker}: ${item.text}`).join('\n'),
      summary: transcriptData.summary,
      action_items: transcriptData.actionItems.map(item => item.action),
      important_points: transcriptData.importantPoints,
      open_questions: transcriptData.openQuestions.map(item => item.question)
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'X-API-Key': API_KEY || '',
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Server error response:', errorData);
      throw new Error(errorData.detail || `Failed to generate Word document (${response.status})`);
    }

    const blob = await response.blob();
    if (blob.size === 0) {
      throw new Error('Generated Word document is empty');
    }

    return blob;
  } catch (error) {
    console.error('Error generating Word document:', error);
    throw error;
  }
};
