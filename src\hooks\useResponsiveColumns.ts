
import { useState, useEffect, useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { TrackerItem } from '@/hooks/useTrackerItems';

const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  xxl: 1536
};

export type ColumnPriority = 'essential' | 'high' | 'medium' | 'low';

export interface ColumnConfig {
  id: string;
  priority: ColumnPriority;
  visible: boolean;
}

export function useResponsiveColumns(
  allColumns: ColumnDef<TrackerItem>[],
  initialConfig?: Record<string, ColumnConfig>
) {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [columnVisibility, setColumnVisibility] = useState<Record<string, boolean>>({});
  const [manualOverrides, setManualOverrides] = useState<Record<string, boolean>>({});
  
  const defaultColumnConfig = useMemo(() => {
    const config: Record<string, ColumnConfig> = {};
    
    allColumns.forEach(column => {
      const columnDef = column as any;
      const id = String(columnDef.accessorKey || column.id);
      
      let priority: ColumnPriority = 'medium';
      
      if (['activity', 'activityStatus'].includes(id)) {
        priority = 'essential';
      } else if (['projectName', 'activityType', 'assignedTo', 'assignedOn'].includes(id)) {
        priority = 'high';
      } else if (['eta', 'comments', 'meetingId'].includes(id)) {
        priority = 'medium';
      } else {
        priority = 'low';
      }
      
      config[id] = {
        id,
        priority,
        visible: true
      };
    });
    
    return config;
  }, [allColumns]);
  
  const columnConfig = useMemo(() => {
    return { ...defaultColumnConfig, ...initialConfig };
  }, [defaultColumnConfig, initialConfig]);
  
  // Load preferences from localStorage with better error handling
  useEffect(() => {
    try {
      const savedPreferences = localStorage.getItem('tracker-column-preferences');
      if (savedPreferences) {
        const preferences = JSON.parse(savedPreferences);
        // Validate that the preferences are valid
        if (typeof preferences === 'object' && preferences !== null) {
          setManualOverrides(preferences);
        }
      }
    } catch (error) {
      console.warn('Failed to parse saved column preferences:', error);
      localStorage.removeItem('tracker-column-preferences');
    }
  }, []);
  
  const savePreferences = (preferences: Record<string, boolean>) => {
    try {
      localStorage.setItem('tracker-column-preferences', JSON.stringify(preferences));
      setManualOverrides(preferences);
    } catch (error) {
      console.warn('Failed to save column preferences:', error);
    }
  };
  
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Enhanced responsive column visibility logic
  useEffect(() => {
    const newVisibility: Record<string, boolean> = {};
    
    Object.values(columnConfig).forEach(config => {
      // Manual overrides take precedence
      if (manualOverrides.hasOwnProperty(config.id)) {
        newVisibility[config.id] = manualOverrides[config.id];
        return;
      }
      
      // Show all columns by default on all screen sizes
      // Only hide low priority columns on very small screens
      if (windowWidth < BREAKPOINTS.md) {
        // Mobile - hide only low priority
        newVisibility[config.id] = config.priority !== 'low';
      } else {
        // All other screens - show everything
        newVisibility[config.id] = true;
      }
    });
    
    setColumnVisibility(newVisibility);
  }, [windowWidth, columnConfig, manualOverrides]);
  
  const visibleColumns = useMemo(() => {
    return allColumns.filter(column => {
      const columnDef = column as any;
      const id = String(columnDef.accessorKey || column.id);
      return columnVisibility[id] !== false;
    });
  }, [allColumns, columnVisibility]);
  
  const toggleColumnVisibility = (columnId: string) => {
    const newValue = !columnVisibility[columnId];
    const newOverrides = { ...manualOverrides, [columnId]: newValue };
    savePreferences(newOverrides);
  };
  
  const showAllColumns = () => {
    const allVisible: Record<string, boolean> = {};
    Object.keys(columnConfig).forEach(id => {
      allVisible[id] = true;
    });
    savePreferences(allVisible);
  };
  
  const showEssentialOnly = () => {
    const essentialOnly: Record<string, boolean> = {};
    Object.values(columnConfig).forEach(config => {
      essentialOnly[config.id] = config.priority === 'essential';
    });
    savePreferences(essentialOnly);
  };
  
  const resetToDefaults = () => {
    localStorage.removeItem('tracker-column-preferences');
    setManualOverrides({});
  };
  
  const visibleColumnCount = Object.values(columnVisibility).filter(Boolean).length;
  const totalColumnCount = Object.keys(columnConfig).length;
  
  return {
    visibleColumns,
    columnVisibility,
    toggleColumnVisibility,
    showAllColumns,
    showEssentialOnly,
    resetToDefaults,
    windowWidth,
    visibleColumnCount,
    totalColumnCount
  };
}
