
import { render, screen } from '@testing-library/react';
import { ResultChart } from '../../ResultChart';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { QueryResultData } from '@/components/dashboard/models';

// Mock dependencies
vi.mock('../ChartDataProcessor', () => ({
  processChartData: vi.fn().mockImplementation(() => ({
    chartData: [{ category: 'A', value: 10 }],
    categoryAxis: 'category',
    dataColumns: ['value'],
    maxValue: 10,
    hasSufficientData: true,
    chartType: 'verticalBar',
    queryType: 'count'
  }))
}));

vi.mock('../ChartTypes', () => ({
  VerticalBarChart: () => <div data-testid="vertical-bar-chart">Vertical Bar Chart</div>,
  HorizontalBarChart: () => <div data-testid="horizontal-bar-chart">Horizontal Bar Chart</div>
}));

vi.mock('../ChartTitle', () => ({
  ChartTitle: ({ query, categoryAxis, dataColumns }) => (
    <div data-testid="chart-title">Chart Title: {categoryAxis}</div>
  )
}));

describe('ResultChart', () => {
  const mockQueryResult: QueryResultData = {
    query: 'SELECT * FROM data',
    tableData: {
      columns: ['category', 'value'],
      rows: [{ category: 'A', value: 10 }, { category: 'B', value: 20 }]
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state when no data is available', () => {
    render(<ResultChart />);
    expect(screen.getByText(/No data available for chart visualization/i)).toBeInTheDocument();
  });

  it('renders vertical bar chart when data is available', () => {
    render(<ResultChart queryResult={mockQueryResult} />);
    expect(screen.getByTestId('chart-title')).toBeInTheDocument();
    expect(screen.getByTestId('vertical-bar-chart')).toBeInTheDocument();
  });

  it('applies correct size classes based on size prop', () => {
    const { rerender } = render(<ResultChart size="small" queryResult={mockQueryResult} />);
    expect(document.querySelector('.h-40')).toBeInTheDocument();
    
    rerender(<ResultChart size="large" queryResult={mockQueryResult} />);
    expect(document.querySelector('.h-96')).toBeInTheDocument();
    
    rerender(<ResultChart size="default" queryResult={mockQueryResult} />);
    expect(document.querySelector('.h-64')).toBeInTheDocument();
  });
});
