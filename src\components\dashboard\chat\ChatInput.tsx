"use client"

import React, { useEffect } from "react"
import { AttachmentButton, VoiceInputButton, FilePreview, AttachmentDialog } from "./index"
import PredictionDropdown from "./PredictionDropdown"
import { useFileAttachments } from "@/hooks/useFileAttachments"
import { useVoiceInput } from "@/hooks/useVoiceInput"
import { useChatKeyboardEvents } from "@/hooks/useChatKeyboardEvents"
import { ChatInputProps } from "./types"
import { PowerModeButton } from "./PowerModeButton"
import InputWrapper from "./InputWrapper"
import { useChatInput } from "./useChatInput"
import { useAppSelector } from "@/hooks/useRedux"
import ErrorDialog from "@/components/ui/ErrorDialog"
import { Button } from "@/components/ui/button"
import { Send } from "lucide-react"

const ChatInput = ({
  inputValue,
  setInputValue,
  handleSubmit,
  activateVoiceInput,
  isLoading = false,
  dashboardType = 3, // Default to DADA dashboard
}: ChatInputProps) => {
  const [selectedMode, setSelectedMode] = React.useState('Default Query');

  const {
    textareaRef,
    formRef,
    inputWrapperRef,
    predictions,
    showPredictions,
    setShowPredictions,
    isChainSequence,
    powerModeEnabled,
    handlePowerModeToggle,
    onPredictionSelect,
    handleFocus,
    getPlaceholder,
    isPredictionsEnabled
  } = useChatInput({
    inputValue,
    setInputValue,
    handleSubmit,
    isLoading,
    dashboardType
  });

  const {
    files,
    isAttachmentDialogOpen,
    setIsAttachmentDialogOpen,
    handleFileChange,
    removeFile
  } = useFileAttachments();

  const {
    isListening,
    setIsListening,
    toggleVoiceInput
  } = useVoiceInput(setInputValue, isLoading);

  const { handleKeyDown } = useChatKeyboardEvents({
    inputValue,
    isLoading,
    predictions,
    textareaRef,
    handleSubmit,
    handlePredictionSelect: onPredictionSelect,
    setInputValue,
    setShowPredictions: setShowPredictions
  });

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoading && inputValue.trim()) {
      setShowPredictions(false); // Hide predictions on submit
      handleSubmit(e);
    }
  };

  const handleModeSelect = (mode: string) => {
    setSelectedMode(mode);
    // Clear input when switching modes
    if (mode === 'Default Query') {
      setInputValue('');
    }
  };

  // Update placeholder text based on selected mode
  const getModePlaceholder = () => {
    switch (selectedMode) {
      case 'Power Query':
        return "Type @ for power keywords...";
      case 'Meta Data':
        return "Search metadata...";
      default:
        return "Ask anything... (Toggle power icon for keywords)";
    }
  };

  return (
    <>
      <form ref={formRef} onSubmit={handleFormSubmit} className="relative flex flex-col items-center px-4">
        <FilePreview files={files} removeFile={removeFile} />

        {/* Mode display text */}
        {dashboardType === 3 && (
          <div className="absolute top-[-35px] left-5 text-sm text-gray-600">
            <Button variant="white">{selectedMode}</Button>
            
          </div>
        )}

        {/* Position prediction dropdown above the input - only show for DADA dashboard type */}
        {isPredictionsEnabled && showPredictions && predictions.length > 0 && (
          <div className="absolute bottom-full left-0 right-0 mb-1 z-50">
            <PredictionDropdown 
              predictions={predictions} 
              inputValue={inputValue} 
              onSelect={onPredictionSelect}
              autoChainPredictions={isChainSequence}
            />
          </div>
        )}

        <div className="relative flex items-center w-full">
          {dashboardType === 3 && (
            <PowerModeButton 
              enabled={powerModeEnabled}
              onToggle={handlePowerModeToggle}
              selectedMode={selectedMode}
              onModeSelect={handleModeSelect}
            />
          )}

          {/* Show attachment button only for Dashboard-1 */}
          {dashboardType === 1 && (
            <div className="absolute left-3 z-10">
              <AttachmentButton onOpenAttachmentDialog={() => setIsAttachmentDialogOpen(true)} />
            </div>
          )}

          <div ref={inputWrapperRef} className="relative w-full">
            <InputWrapper
              ref={textareaRef}
              dashboardType={dashboardType}
              placeholder={getModePlaceholder()}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              disabled={isLoading || isListening}
            />
          </div>

          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            {dashboardType === 3 ? (
              <Button
                type="submit"
                size="icon"
                className={`rounded-full focus:ring-0 focus:outline-none ${
                  inputValue.trim() 
                    ? "bg-blue-500 text-white hover:bg-blue-600" 
                    : "bg-gray-300 text-gray-600 cursor-not-allowed"
                }`}
                disabled={!inputValue.trim() || isLoading}
              >
                <Send size={18} />
              </Button>
            ) : (
              <VoiceInputButton 
                isListening={isListening}
                setIsListening={setIsListening}
                isLoading={isLoading}
                setInputValue={setInputValue}
                onActivate={() => toggleVoiceInput(activateVoiceInput)}
              />
            )}
          </div>
        </div>

        <AttachmentDialog 
          isOpen={isAttachmentDialogOpen}
          onOpenChange={setIsAttachmentDialogOpen}
          handleFileChange={handleFileChange}
        />
      </form>
      
      {/* Add ErrorDialog - it will only show when there's an error */}
      <ErrorDialog />
    </>
  );
}

export default ChatInput
