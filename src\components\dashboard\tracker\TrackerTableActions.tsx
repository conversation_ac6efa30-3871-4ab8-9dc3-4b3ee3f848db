
import React from 'react';
import { Button } from '@/components/ui/button';

interface TrackerTableActionsProps {
  onSave: () => void;
  onCancel: () => void;
  isSaving: boolean;
  hasChanges: boolean;
}

const TrackerTableActions: React.FC<TrackerTableActionsProps> = ({
  onSave,
  onCancel,
  isSaving,
  hasChanges
}) => {
  return (
    <div className="flex justify-between p-1">
      <Button
        variant="outline"
        onClick={onCancel}
        className="flex items-center"
      >
        Cancel
      </Button>
      <Button
        onClick={onSave}
        disabled={isSaving || !hasChanges}
        variant={hasChanges ? 'greenmind' : 'secondary'}
      >
        {isSaving ? (
          <>
            <div className="h-4 w-4 mr-1 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
            Saving...
          </>
        ) : (
          <>
            Save Changes
          </>
        )}
      </Button>
    </div>
  );
};

export default TrackerTableActions;
