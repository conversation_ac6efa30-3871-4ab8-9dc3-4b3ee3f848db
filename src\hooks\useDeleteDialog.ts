
import { useState } from 'react';
import { useAppDispatch } from '@/hooks/useRedux';
import { useNavigate } from 'react-router-dom';
import { setPersonalFiles, setProjectFiles } from '@/stores/fileSlice';
import { deleteFileByFolderType } from '@/services/api/audioTranscript';
import { FolderType } from '@/services/api/audioTranscript/types';

interface FileToDelete {
  fileName: string;
  folderType: FolderType;
}

interface DeleteStatus {
  status: 'idle' | 'loading' | 'success' | 'error';
  errorCode?: number;
}

export const useDeleteDialog = (
  personalFiles: string[], 
  projectFiles: string[],
  clearFileInput?: () => void
) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<FileToDelete | null>(null);
  const [deleteStatus, setDeleteStatus] = useState<DeleteStatus>({ status: 'idle' });
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const handleDeleteClick = (fileName: string, folderType: FolderType) => {
    if (folderType === 'tracker') return;
    setFileToDelete({ fileName, folderType });
    setIsDeleteDialogOpen(true);
    setDeleteStatus({ status: 'idle' }); // Reset status on new delete
  };

  const handleDelete = async () => {
    if (!fileToDelete) return;

    const { fileName, folderType } = fileToDelete;
    setDeleteStatus({ status: 'loading' });

    try {
      await deleteFileByFolderType(folderType, fileName);
      setDeleteStatus({ status: 'success' });
      
      // Update store after successful deletion
      if (folderType === 'personal') {
        dispatch(setPersonalFiles(personalFiles.filter(file => file !== fileName)));
      } else if (folderType === 'project') {
        dispatch(setProjectFiles(projectFiles.filter(file => file !== fileName)));
      }

      // Wait for success message to show before cleanup
      setTimeout(() => {
        if (clearFileInput) {
          clearFileInput();
        }
        setFileToDelete(null);
        setIsDeleteDialogOpen(false);
        setDeleteStatus({ status: 'idle' });
        navigate('/transcript');
      }, 4000); // Match the timeout in DeleteConfirmationDialog

    } catch (error) {
      setDeleteStatus({ 
        status: 'error', 
        errorCode: error.status || 500 
      });
      
      // Reset status after error display
      setTimeout(() => {
        setDeleteStatus({ status: 'idle' });
      }, 3000); // Match the timeout in DeleteConfirmationDialog
    }
  };

  return {
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    fileToDelete,
    deleteStatus,
    handleDeleteClick,
    handleDelete
  };
};
