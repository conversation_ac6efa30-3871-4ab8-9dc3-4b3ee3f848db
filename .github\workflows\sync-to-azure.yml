name: Mirror GitHub main to Azure DevOps Transcript_dev

on:
  push:
    branches:
      - main # Trigger when pushing to GitHub main branch

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Get all history - VERY IMPORTANT
          ref: main

      - name: Configure Git
        run: |
          git config --global user.name "Anand-MLZ"
          git config --global user.email "<EMAIL>"

      - name: Pull latest from Azure DevOps Transcript_dev
        run: |
          git remote add azure https://anything:${{ secrets.AZUREPAT }}@dev.azure.com/mindlabz/MLZ_AI_Products/_git/MLZ_TRANSCRIPT_AI
          git fetch azure Transcript_dev:Transcript_dev  # Fetch and merge remote Transcript_dev
          git merge azure/Transcript_dev --allow-unrelated-histories -m "Merge Azure DevOps Transcript_dev changes"
        env:
          AZURE_DEVOPS_PAT: ${{ secrets.AZUREPAT }}

      - name: Push to Azure DevOps
        run: |
          git push azure main:Transcript_dev  # Push GitHub main to Azure DevOps Transcript_dev
        env:
          AZURE_DEVOPS_PAT: ${{ secrets.AZUREPAT }}



