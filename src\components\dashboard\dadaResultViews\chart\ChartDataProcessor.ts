
import type { QueryResultData } from "@/components/dashboard/models";
import { getChartType } from "./ChartUtils";

interface ProcessedChartData {
  chartData: any[];
  categoryAxis: string;
  dataColumns: string[];
  maxValue: number;
  hasSufficientData: boolean;
  chartType: string;
  queryType: string;
}

export const processChartData = (queryResult: QueryResultData | null): ProcessedChartData => {
  const defaultResult = {
    chartData: [],
    categoryAxis: '',
    dataColumns: [],
    maxValue: 0,
    hasSufficientData: false,
    chartType: 'verticalBar',
    queryType: 'default'
  };

  if (!queryResult?.tableData?.rows?.length) {
    console.log("Insufficient data in queryResult:", queryResult);
    return defaultResult;
  }

  const { columns, rows } = queryResult.tableData;
  
  // Convert row data for chart rendering with validation
  const chartData = rows.map(row => {
    const chartDataPoint: any = {};
    for (const key of columns) {
      if (row[key] !== undefined && row[key] !== null) {
        const value = row[key];
        // Ensure numeric values are valid numbers
        if (typeof value === 'number') {
          chartDataPoint[key] = isFinite(value) ? value : 0;
        } else if (typeof value === 'string') {
          const numValue = Number(value);
          chartDataPoint[key] = !isNaN(numValue) && isFinite(numValue) ? numValue : 0;
        } else {
          chartDataPoint[key] = value;
        }
      } else {
        chartDataPoint[key] = 0; // Default value for undefined/null
      }
    }
    return chartDataPoint;
  });

  // Identify numeric columns with validation
  const numericColumns = columns.filter(col => {
    return rows.some(row => {
      const val = row[col];
      if (typeof val === 'number') {
        return isFinite(val);
      }
      if (typeof val === 'string') {
        const numVal = Number(val);
        return !isNaN(numVal) && isFinite(numVal);
      }
      return false;
    });
  });

  // Get maximum value with validation
  const getMaxValue = () => {
    let max = 0;
    chartData.forEach(item => {
      numericColumns.forEach(col => {
        const value = item[col];
        if (typeof value === 'number' && isFinite(value) && value > max) {
          max = value;
        }
      });
    });
    return max > 0 ? max * 1.2 : 100; // Add 20% padding or default to 100 if no valid max
  };

  // Get the first column that's not numeric to use as the category axis
  const categoryAxis = columns.find(col => !numericColumns.includes(col)) || columns[0];
  
  // Get all numeric columns to display as data series
  const dataColumns = numericColumns.filter(col => col !== categoryAxis);
  
  // Check if we have sufficient data to render a chart
  const hasSufficientData = chartData.length > 0 && dataColumns.length > 0;

  // Determine chart type based on query and data
  let chartType = getChartType(queryResult.query, chartData);
  
  // Determine query type for specialized styling
  const queryType = determineQueryType(queryResult.query);

  // Sort data for better visualization based on the chart type
  const sortedChartData = [...chartData];
  if (chartType === 'horizontalBar' && dataColumns.length > 0) {
    sortedChartData.sort((a, b) => {
      const valueKey = dataColumns[0] || Object.keys(a).find(key => typeof a[key] === 'number') || '';
      if (!valueKey) return 0;
      const valueA = a[valueKey] || 0;
      const valueB = b[valueKey] || 0;
      return (valueB) - (valueA);
    });
  }

  // Get maximum value for better axis scaling
  const maxValue = getMaxValue();

  // Use vertical bar chart for non-revenue/city data by default
  if (queryType !== 'revenue' && queryType !== 'city' && chartData.length > 5) {
    chartType = 'verticalBar';
  }

  const result = {
    chartData: sortedChartData,
    categoryAxis: categoryAxis || '',
    dataColumns,
    maxValue,
    hasSufficientData,
    chartType,
    queryType
  };

  console.log("Processed chart data result:", result);
  return result;
};

// Helper function to determine query type
const determineQueryType = (query?: string): string => {
  if (!query) return 'default';
  const queryLower = query.toLowerCase();
  if (queryLower.includes('city')) return 'city';
  if (queryLower.includes('age')) return 'age';
  if (queryLower.includes('revenue') || queryLower.includes('sales')) return 'revenue';
  return 'default';
};
