
import React from "react"
import { Calendar } from "lucide-react"
import { MetricFormData } from "@/stores/metricSlice"

interface ReviewStepProps {
  formData: MetricFormData
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void
}

export const ReviewStep: React.FC<ReviewStepProps> = ({ formData, handleInputChange }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Sample Data</h3>
        <div className="border border-gray-300 rounded overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr className="bg-lime-300">
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Col1</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Col2</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                  MetricCol
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr className="bg-white">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Val1</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Val1</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10</td>
              </tr>
              <tr className="bg-lime-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Val2</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Val2</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">20</td>
              </tr>
              <tr className="bg-white">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Val3</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Val3</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">30</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <label htmlFor="reviewStatus" className="block text-sm font-medium text-gray-700 mb-1">
            Review Status:
          </label>
          <select
            id="reviewStatus"
            name="reviewStatus"
            value={formData.reviewStatus}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 rounded"
          >
            <option value="Pending">Pending</option>
            <option value="Reviewed">Reviewed</option>
            <option value="Rejected">Rejected</option>
          </select>
        </div>
        <div>
          <label htmlFor="reviewDate" className="block text-sm font-medium text-gray-700 mb-1">
            Reviewed On:
          </label>
          <div className="relative">
            <input
              type="text"
              id="reviewDate"
              name="reviewDate"
              value={formData.reviewDate}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
            />
            <Calendar className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <label htmlFor="reviewedBy" className="block text-sm font-medium text-gray-700 mb-1">
            Reviewed By:
          </label>
          <input
            type="text"
            id="reviewedBy"
            name="reviewedBy"
            value={formData.reviewedBy}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Enter name"
          />
        </div>
        <div>
          <label htmlFor="reviewerEmail" className="block text-sm font-medium text-gray-700 mb-1">
            Email:
          </label>
          <input
            type="email"
            id="reviewerEmail"
            name="reviewerEmail"
            value={formData.reviewerEmail}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Enter email"
          />
        </div>
      </div>
    </div>
  )
}
