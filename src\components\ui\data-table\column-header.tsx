
import { Column } from "@tanstack/react-table"
import { ChevronsUpDown, EyeOff, SortAsc, SortDesc, FilterX } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface ColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>
  title: string
  className?: string
}

export function ColumnHeader<TData, TValue>({
  column,
  title,
  className,
}: ColumnHeaderProps<TData, TValue>) {
  const hasFilter = column.getFilterValue();
  
  if (!column.getCanSort()) {
    return (
      <div className={cn("flex items-center justify-between", className)}>
        <span className="text-white font-medium">{title}</span>
        {hasFilter && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => column.setFilterValue("")}
            className="h-6 w-6 p-0 text-white hover:bg-teal-700"
            title="Clear filter"
          >
            <FilterX className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className={cn("flex items-center justify-between", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 text-white hover:bg-teal-700 data-[state=open]:bg-teal-700 flex items-center gap-1"
          >
            <span className="font-medium">{title}</span>
            {column.getIsSorted() === "desc" ? (
              <SortDesc className="h-4 w-4" />
            ) : column.getIsSorted() === "asc" ? (
              <SortAsc className="h-4 w-4" />
            ) : (
              <ChevronsUpDown className="h-4 w-4" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-40">
          <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
            <SortAsc className="mr-2 h-3.5 w-3.5" />
            Sort Ascending
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
            <SortDesc className="mr-2 h-3.5 w-3.5" />
            Sort Descending
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => column.toggleVisibility(false)}>
            <EyeOff className="mr-2 h-3.5 w-3.5" />
            Hide Column
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      {hasFilter && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => column.setFilterValue("")}
          className="h-6 w-6 p-0 text-white hover:bg-teal-700 ml-1"
          title="Clear filter"
        >
          <FilterX className="h-3 w-3" />
        </Button>
      )}
    </div>
  )
}
