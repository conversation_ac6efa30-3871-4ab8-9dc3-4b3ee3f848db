import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Copy, Check } from "lucide-react";
import { toast } from 'sonner';

interface ShareDialogProps {
  isOpen: boolean;
  onClose: () => void;
  fileName: string;
  folderType: "personal" | "project";
}

export const ShareDialog: React.FC<ShareDialogProps> = ({
  isOpen,
  onClose,
  fileName,
  folderType,
}) => {
  const [copied, setCopied] = useState(false);

  // Add /sharedview to the URL
  const shareableLink = `${window.location.origin}/transcript/${folderType}/${fileName}/sharedview`;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(shareableLink);
      setCopied(true);
      toast.success("Link copied to clipboard");
      
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    } catch (err) {
      toast.error("Failed to copy link");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-auto max-w-[90vw] p-6">
        <DialogHeader>
          <DialogTitle className="text-blue-600">Share {fileName}</DialogTitle>
          <DialogDescription>
            Share this link to provide view-only access to this transcript.
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          <div className="relative flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <div className="w-full overflow-hidden text-ellipsis text-sm text-gray-600 pr-10">
              {shareableLink}
            </div>
            <div 
              onClick={handleCopy}
              className="absolute right-3 cursor-pointer hover:text-blue-600 transition-colors"
            >
              {copied ? <Check size={20} /> : <Copy size={20} />}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};



