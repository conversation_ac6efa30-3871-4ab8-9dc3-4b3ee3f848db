import React, { useState, useEffect } from 'react';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { getProjectNames, ProjectListItem } from '@/services/api/admin/projectService';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface ProjectSelectorProps {
  onProjectSelect?: (projectId: string, projectName: string) => void;
}

const ProjectSelector: React.FC<ProjectSelectorProps> = ({ onProjectSelect }) => {
  const [projects, setProjects] = useState<ProjectListItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState<string>("Select");

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      const projectsData = await getProjectNames();
      setProjects(projectsData);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast.error('Failed to load projects');
    } finally {
      setIsLoading(false);
    }
  };

  const handleProjectChange = (value: string) => {
    setSelectedProject(value);
    if (onProjectSelect && value !== "Select") {
      const selectedProjectData = projects.find(p => p.project_id.toString() === value);
      if (selectedProjectData) {
        onProjectSelect(value, selectedProjectData.project_name);
      }
    }
  };

  return (
    <Select 
      value={selectedProject} 
      onValueChange={handleProjectChange}
    >
      <SelectTrigger className="w-full">
        {isLoading ? (
          <div className="flex items-center">
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            <span>Loading projects...</span>
          </div>
        ) : (
          <SelectValue placeholder="Select Project" />
        )}
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="Select">Select Project</SelectItem>
        {projects.map((project) => (
          <SelectItem key={project.project_id} value={project.project_id.toString()}>
            {project.project_name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default ProjectSelector;
