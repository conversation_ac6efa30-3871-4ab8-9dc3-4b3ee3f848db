import { getAccessToken } from '../auth/msalService';

export const callProtectedApi = async (url: string, options: RequestInit = {}) => {
  try {
    // Get a fresh token
    const token = await getAccessToken();
    if (!token) {
      throw new Error('Not authenticated');
    }

    // Add the token to the request
    const headers = {
      ...options.headers,
      'Authorization': `Bearer ${token}`
    };

    // Make the request
    const response = await fetch(url, {
      ...options,
      headers
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Protected API call failed:', error);
    throw error;
  }
};