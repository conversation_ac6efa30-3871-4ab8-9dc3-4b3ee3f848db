
import React, { useState, useEffect, useRef } from 'react';
import { TranscriptTab } from '../models';
import { TranscriptItem } from './types';
import AudioPlayer from './AudioPlayer';
import { useAppSelector } from '@/hooks/useRedux';
import { Virtuoso } from 'react-virtuoso';
import { baseUrl } from '@/services/api/audioTranscript/config';

interface TranscriptTabViewProps {
  activeTab: TranscriptTab;
  transcriptData: {
    transcript: TranscriptItem[];
    audioUrl?: string;
    summary: string;
    actionItems: { action: string; dueDate: string; responsible: string }[];
    importantPoints: string[];
    openQuestions: { question: string }[];
  };
}

const formatTime = (time: number | string | undefined) => {
  if (time === undefined) return '0:00';
  
  if (typeof time === 'string') {
    // If it's already in format like "0:02", return it
    if (time.includes(':')) return time;
    return parseFloat(time).toFixed(0);
  }
  
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
};

// Add this function to parse timestamps from the format "0:02" to seconds
const parseTimestamp = (timestamp: string): number => {
  if (!timestamp) return 0;
  const parts = timestamp.split(':');
  if (parts.length === 2) {
    return parseInt(parts[0]) * 60 + parseInt(parts[1]);
  }
  if (parts.length === 3) {
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
  }
  return 0;
};

const TranscriptTabView: React.FC<TranscriptTabViewProps> = ({ activeTab, transcriptData }) => {
  const [currentSpeakingIndex, setCurrentSpeakingIndex] = useState<number | null>(null);
  const [highlightedWord, setHighlightedWord] = useState<{index: number, wordIndex: number} | null>(null);
  const [isAudioLoading, setIsAudioLoading] = useState(true);
  const currentTranscript = useAppSelector(state => state.file.currentTranscript);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const virtuosoRef = useRef(null);
  const timeUpdateRef = useRef<number | null>(null);
  
  // Log the transcriptData for debugging
  useEffect(() => {
    console.log("TranscriptTabView received data:", transcriptData);
  }, [transcriptData]);
  
  // Ensure we're using the correct audioUrl - prioritize the one passed directly in transcriptData
  const audioUrl = transcriptData.audioUrl || 
                  (currentTranscript?.data as any)?.audioUrl;
  
  // Log the audio URL for debugging
  useEffect(() => {
    if (audioUrl) {
      console.log("Audio URL available for player:", audioUrl);
    } else {
      console.warn("No audio URL found - audio player will not work");
    }
  }, [audioUrl]);

  const handleTimeUpdate = (currentTime: number) => {
    if (!transcriptData.transcript || transcriptData.transcript.length === 0) {
      return;
    }
    
    // Throttle updates
    if (timeUpdateRef.current && Date.now() - timeUpdateRef.current < 100) {
      return;
    }
    timeUpdateRef.current = Date.now();
    
    // Find the current speaking segment based on estimated times
    const index = transcriptData.transcript.findIndex((item, idx) => {
      // Convert timestamp to seconds
      const startTime = item.startTime || 
                       (item.timestamp ? parseTimestamp(item.timestamp) : 0);
      
      // Estimate end time based on next item's start time
      const nextItem = transcriptData.transcript[idx + 1];
      const endTime = nextItem ? 
                     (nextItem.startTime || (nextItem.timestamp ? parseTimestamp(nextItem.timestamp) : 0)) : 
                     startTime + 30; // Assume 30 seconds if no next item
      
      return currentTime >= startTime && currentTime < endTime;
    });
    
    if (index !== -1 && index !== currentSpeakingIndex) {
      setCurrentSpeakingIndex(index);
      // Skip word-level highlighting since we don't have word timing data
    }
  };

  // Handle click on transcript item to play from that point
  const handleTranscriptItemClick = (index: number) => {
    if (!audioRef.current || !transcriptData.transcript[index]) return;
    
    const item = transcriptData.transcript[index];
    let startTime = 0;
    
    if (item.startTime !== undefined) {
      startTime = typeof item.startTime === 'number' ? item.startTime : 
                 typeof item.startTime === 'string' ? parseFloat(item.startTime) : 0;
    } else if (item.timestamp) {
      // Parse timestamp format like "0:02"
      startTime = parseTimestamp(item.timestamp);
    }
    
    audioRef.current.currentTime = startTime;
    setCurrentSpeakingIndex(index);
    
    audioRef.current.play().catch(err => {
      console.error("Error playing audio:", err);
    });
  };

  // Handle click on individual word to play from that word's time
  const handleWordClick = (sentenceIndex: number, wordIndex: number, item: TranscriptItem) => {
    if (!audioRef.current) return;
    
    // Set the highlighted word immediately for visual feedback
    setHighlightedWord({ index: sentenceIndex, wordIndex });
    
    // Calculate the time for this word
    let wordTime = 0;
    
    // If we have word-level timing data
    if (item.words && Array.isArray(item.words) && item.words.length > wordIndex) {
      const word = item.words[wordIndex];
      wordTime = typeof word.startTime === 'number' ? word.startTime : 
                typeof word.startTime === 'string' ? parseFloat(word.startTime) : 0;
    } else {
      // Approximate word timing based on position in sentence
      const startTime = typeof item.startTime === 'number' ? item.startTime : 
                       typeof item.startTime === 'string' ? parseFloat(item.startTime) : 0;
      const endTime = typeof item.endTime === 'number' ? item.endTime : 
                     typeof item.endTime === 'string' ? parseFloat(item.endTime) : startTime + 10;
      
      const totalDuration = endTime - startTime;
      const wordPosition = wordIndex / item.text.split(' ').length;
      wordTime = startTime + (totalDuration * wordPosition);
    }
    
    // Set audio time and play
    audioRef.current.currentTime = wordTime;
    audioRef.current.play().catch(err => {
      // console.error("Error playing audio:", err);
    });
  };

  // Render text with highlighted word
  const renderText = (item: TranscriptItem, index: number) => {
    return (
      <p className={`text-sm ${currentSpeakingIndex === index ? 'text-blue-800 font-medium' : 'text-gray-800'}`}>
        {item.text}
      </p>
    );
  };

  // Add this effect to ensure smooth scrolling to the current segment
  useEffect(() => {
    if (activeTab === 'transcript' && currentSpeakingIndex !== null && virtuosoRef.current) {
      virtuosoRef.current.scrollToIndex({
        index: currentSpeakingIndex,
        align: 'center',
        behavior: 'smooth'
      });
    }
  }, [activeTab, currentSpeakingIndex]);

  useEffect(() => {
    if (activeTab === 'transcript' && audioUrl) {
      setIsAudioLoading(true);
    }
  }, [activeTab, audioUrl]);

  const getContentByTab = () => {
    switch (activeTab) {
      case 'transcript':
        return (
          <div className="p-4 pb-20"> {/* Add padding at bottom for the audio player */}
            {transcriptData.transcript.length === 0 ? (
              <div className="flex items-center justify-center p-8">
                <p className="text-gray-500">No transcript data available</p>
              </div>
            ) : (
              <Virtuoso
                ref={virtuosoRef}
                style={{ height: 'calc(100vh - 250px)' }}
                totalCount={transcriptData.transcript.length}
                itemContent={(index) => {
                  const item = transcriptData.transcript[index];
                  return (
                    <div 
                      id={`transcript-item-${index}`}
                      className={`p-3 mb-2 rounded-lg cursor-pointer transition-colors ${
                        currentSpeakingIndex === index ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => handleTranscriptItemClick(index)}
                    >
                      <div className="flex items-start">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white mr-3 ${
                          item.speaker?.toLowerCase().includes('speaker a') ? 'bg-blue-500' : 
                          item.speaker?.toLowerCase().includes('speaker b') ? 'bg-green-500' : 
                          item.speaker?.toLowerCase().includes('speaker c') ? 'bg-purple-500' :
                          item.speaker?.toLowerCase().includes('speaker d') ? 'bg-orange-500' :
                          'bg-gray-500'
                        }`}>
                          {item.speaker?.charAt(item.speaker.indexOf(' ') + 1) || 'S'}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center mb-1">
                            <p className="text-sm font-medium text-gray-900 mr-2">{item.speaker || 'Speaker'}</p>
                            <p className="text-xs text-gray-500">
                              {item.timestamp || formatTime(item.startTime)}
                            </p>
                          </div>
                          {renderText(item, index)}
                        </div>
                      </div>
                    </div>
                  );
                }}
              />
            )}
          </div>
        );
      case 'summary':
        return (
          <div className="p-4">
            <h3 className="text-base font-medium mb-3">Summary</h3>
            <p className="text-sm text-gray-800">{transcriptData.summary}</p>
          </div>
        );
      case 'actionItems':
        return (
          <div className="p-4">
            <h3 className="text-base font-medium mb-3">Action Items</h3>
            <ul className="list-disc pl-5 space-y-2">
              {transcriptData.actionItems.map((item, index) => (
                <li key={index} className="text-sm text-gray-800">
                  {typeof item === 'string' ? item : item.action}
                </li>
              ))}
            </ul>
          </div>
        );
      case 'notes':
        return (
          <div className="p-4">
            <h3 className="text-base font-medium mb-3">Important Points</h3>
            <ul className="list-disc pl-5 space-y-2">
              {transcriptData.importantPoints.map((point, index) => (
                <li key={index} className="text-sm text-gray-800">{point}</li>
              ))}
            </ul>
          </div>
        );
      case 'openQuestions':
        return (
          <div className="p-4">
            <h3 className="text-base font-medium mb-3">Open Questions</h3>
            <ul className="list-disc pl-5 space-y-2">
              {transcriptData.openQuestions.map((question, index) => (
                <li key={index} className="text-sm text-gray-800">
                  {typeof question === 'string' ? question : question.question}
                </li>
              ))}
            </ul>
          </div>
        );
      default:
        return null;
    }
  };

  // Debugging logs to check data structure
  useEffect(() => {
    if (audioUrl) {
      console.log("Audio URL available:", audioUrl);
    } else {
      // console.warn("No audio URL found - word highlighting will not work");
    }
    
    // Check if transcript data has timing information
    const hasTiming = transcriptData.transcript?.some(item => 
      item.startTime !== undefined && item.endTime !== undefined
    );
    
    // console.log("Transcript has timing information:", hasTiming);
    
    if (transcriptData.transcript?.length > 0) {
      console.log("Sample transcript item:", transcriptData.transcript[0]);
      
      // Check for word-level timing
      const hasWordTiming = transcriptData.transcript.some(item => 
        item.words && Array.isArray(item.words) && item.words.length > 0
      );
      
      // console.log("Transcript has word-level timing:", hasWordTiming);
    }
  }, [transcriptData, audioUrl]);

  return (
    <div className="bg-white flex flex-col h-full relative">
     
        {getContentByTab()}
    
      {activeTab === 'transcript' && audioUrl && (
        <>
          {isAudioLoading && (
            <div className="fixed bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200 flex justify-center">
              <div className="animate-pulse flex items-center">
                <div className="h-4 w-4 bg-blue-500 rounded-full mr-2"></div>
                <p className="text-sm text-gray-600">Loading audio player...</p>
              </div>
            </div>
          )}
          <AudioPlayer 
            audioUrl={audioUrl} 
            transcript={transcriptData.transcript}
            onTimeUpdate={handleTimeUpdate}
            ref={audioRef}
            onLoadStart={() => setIsAudioLoading(true)}
            onCanPlay={() => setIsAudioLoading(false)}
          />
        </>
      )}
    </div>
  );
};

export default TranscriptTabView;
