
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FileDown } from "lucide-react";
import { toast } from 'sonner';
import { exportTranscriptToPPT, exportTranscriptToWord } from '@/services/api/audioTranscript';
import { TranscriptItem } from './types';

interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  fileName: string;
  folderType: "personal" | "project";
  transcriptData: {
    transcript: TranscriptItem[];
    summary: string;
    actionItems: { action: string; dueDate: string; responsible: string }[];
    importantPoints: string[];
    openQuestions: { question: string }[];
  };
}

export const ExportDialog: React.FC<ExportDialogProps> = ({
  isOpen,
  onClose,
  fileName,
  folderType,
  transcriptData
}) => {
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async (format: 'docx' | 'ppt') => {
    setIsExporting(true);
    try {
      // Use original filename without modification
      console.log('Processing file:', fileName);

      let blob: Blob;
      let fileExtension: string;
      let successMessage: string;

      if (format === 'ppt') {
        blob = await exportTranscriptToPPT(fileName, folderType, transcriptData);
        fileExtension = 'pptx';
        successMessage = 'Your file has been successfully exported as PowerPoint';
      } else {
        blob = await exportTranscriptToWord(fileName, folderType, transcriptData);
        fileExtension = 'docx';
        successMessage = 'Your file has been successfully exported as Word document';
      }

      // Create download link with original filename
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      // Only modify the output filename, not the API request filename
      const outputName = fileName.split('.')[0]; // Remove extensions only for the downloaded file
      a.download = `${outputName}_summary.${fileExtension}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      onClose();

      toast.success(successMessage, {
        style: { backgroundColor: 'green', color: 'white' }
      });
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Export failed', {
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent aria-describedby="export-dialog-description">
        <DialogHeader>
          <DialogTitle className="text-blue-600">Export {fileName}</DialogTitle>
          <DialogDescription id="export-dialog-description">
            Choose your preferred export format to download the file.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex flex-col gap-3">
            <Button
              onClick={() => handleExport('docx')}
              disabled={isExporting}
              className="flex items-center justify-center"
            >
              <FileDown className="mr-2 h-4 w-4" />
              {isExporting ? 'Generating Word document...' : 'Download as Word Document'}
            </Button>
            <Button
              onClick={() => handleExport('ppt')}
              disabled={isExporting}
              className="flex items-center justify-center"
            >
              <FileDown className="mr-2 h-4 w-4" />
              {isExporting ? 'Generating PPT...' : 'Download as PowerPoint'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
  };
