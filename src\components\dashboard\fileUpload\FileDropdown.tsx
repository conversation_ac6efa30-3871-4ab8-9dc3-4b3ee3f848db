
import React from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { FileDropdownProps } from './types';

const FileDropdown = ({ 
  uploadedFiles, 
  selectedFile, 
  selectFile, 
  dropdownOpen, 
  setDropdownOpen 
}: FileDropdownProps) => {
  return (
    <div className="relative mt-3">
      <div 
        className="border border-gray-300 rounded p-3 flex items-center justify-between cursor-pointer"
        onClick={() => setDropdownOpen(!dropdownOpen)}
      >
        <div>{selectedFile ? selectedFile.name : 'Select a file'}</div>
        {dropdownOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>
      
      {dropdownOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded shadow-lg">
          {uploadedFiles.map((file, index) => (
            <div 
              key={index} 
              className="p-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => selectFile(file)}
            >
              {file.name}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FileDropdown;
