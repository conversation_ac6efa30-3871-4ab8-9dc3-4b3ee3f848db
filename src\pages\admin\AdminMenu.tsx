import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Plus, Edit, Trash } from 'lucide-react';
import { toast } from 'sonner';
import ProjectForm from '@/components/admin/ProjectForm';
import { getProjects, deleteProject } from '@/services/api/admin/projectService';
import { useNavigate } from 'react-router-dom';
import DeleteConfirmDialog from '@/components/admin/DeleteConfirmDialog';
import { useAppDispatch } from '@/hooks/useRedux';
import { refreshSidebarProjects } from '@/stores/projectSlice';

const AdminMenu: React.FC = () => {
  const [projects, setProjects] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [editingProject, setEditingProject] = useState<any>(null);
  const [projectToDelete, setProjectToDelete] = useState<any>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      const projectsData = await getProjects();
      setProjects(projectsData);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast.error('Failed to load projects');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditClick = (project: any) => {
    setEditingProject(project);
    setIsCreating(false);
  };

  const handleDeleteClick = (project: any) => {
    setProjectToDelete(project);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!projectToDelete) return;
    
    try {
      await deleteProject(projectToDelete.project_id);
      setProjects(projects.filter(p => p.project_id !== projectToDelete.project_id));
      
      // Refresh sidebar projects
      dispatch(refreshSidebarProjects());
    } catch (error) {
      console.error('Error deleting project:', error);
      throw error;
    }
  };

  const handleSaveProject = (project: any) => {
    if (editingProject) {
      // Update existing project
      setProjects(projects.map(p => p.project_id === project.project_id ? project : p));
      setEditingProject(null);
    } else {
      // Add new project
      setProjects([...projects, project]);
      setIsCreating(false);
    }
    
    // Refresh projects from API after save
    fetchProjects();
    
    // Refresh sidebar projects
    dispatch(refreshSidebarProjects());
  };

  const handleCancelEdit = () => {
    setIsCreating(false);
    setEditingProject(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-bold">Project Management</h1>
        {!isCreating && !editingProject && (
          <Button onClick={() => setIsCreating(true)} variant='outline'>
            <Plus className="mr-2 h-4 w-4" />
            New Project
          </Button>
        )}
      </div>

      {(isCreating || editingProject) ? (
        <ProjectForm 
          onSave={handleSaveProject} 
          onCancel={handleCancelEdit}
          initialData={editingProject}
        />
      ) : isLoading ? (
        <div className="text-center py-8">
          <p className="text-gray-500">Loading projects...</p>
        </div>
      ) : projects.length > 0 ? (
        <div className="border rounded-md">
          <table className="w-full">
            <thead>
              <tr className="border-b bg-gray-50 text-sm">
                <th className="px-4 py-2 text-left">ID</th>
                <th className="px-4 py-2 text-left">Name</th>
                <th className="px-4 py-2 text-left">Owner</th>
                <th className="px-4 py-2 text-left">Status</th>
                <th className="px-4 py-2 text-right">Actions</th>
              </tr>
            </thead>
            <tbody>
              {projects.map((project) => (
                <tr key={project.project_id} className="border-b text-sm">
                  <td className="px-4 py-3">{project.project_id}</td>
                  <td className="px-4 py-3">{project.project_name}</td>
                  <td className="px-4 py-3">{project.project_owner}</td>
                  <td className="px-4 py-3">{project.project_status}</td>
                  <td className="px-4 py-3 text-right">
                    <div className="flex justify-end space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleEditClick(project)}
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleDeleteClick(project)}
                      >
                        <Trash className="h-4 w-4 text-red-500" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-8 border rounded-md">
          <p className="text-gray-500">No projects found. Create your first project!</p>
        </div>
      )}

      <DeleteConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Project"
        description={`Are you sure you want to delete "${projectToDelete?.project_name}"? This action cannot be undone.`}
      />
    </div>
  );
};

export default AdminMenu;





