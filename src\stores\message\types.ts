
import { QueryResultData } from '@/components/dashboard/models';

// Define types
export interface Message {
  type: 'query' | 'response';
  content: string;
  minimized: boolean;
  queryResult?: QueryResultData;
}

export interface MessageState {
  messages: Message[];
  filteredMessages: Message[];
  inputValue: string;
  chatbotInputValue: string;
  transcriptInputValue: string;
  dadaInputValue: string;
  isLoading: boolean;
  searchQuery: string;
  headerSearchQuery: string;
  uploadedFile: any;
}

// Initial state
export const initialState: MessageState = {
  messages: [],
  filteredMessages: [],
  inputValue: '',
  chatbotInputValue: '',
  transcriptInputValue: '',
  dadaInputValue: '',
  isLoading: false,
  searchQuery: '',
  headerSearchQuery: '',
  uploadedFile: null,
};
