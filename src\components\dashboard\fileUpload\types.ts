
import { TranscriptTab, UploadedFile } from '../models';

// Define transcript item interface
export interface WordTiming {
  word: string;
  startTime: number;
  endTime: number;
}

export interface TranscriptItem {
  speaker: string;
  text: string;
  timestamp: string;
  startTime?: number;
  endTime?: number;
  words?: WordTiming[]; // Add word-level timing information
}

export interface TranscriptData {
  transcript: TranscriptItem[]; // Only support new format
  file_id: number;
  summary: string;
  actionItems: { action: string; dueDate: string; responsible: string }[];
  importantPoints: string[];
  openQuestions: { question: string }[];
}

export interface TranscriptResponse {
  message: string;
  file_id: number;
  filename: string;
  transcript: TranscriptItem[];
  summary: {
    summary: string;
    actionItems: { action: string; dueDate: string; responsible: string }[];
    importantPoints: string[];
    openQuestions: { question: string }[];
  };
  date: string;
}

// Make sure ExtendedUploadedFile interface is properly defined
export interface ExtendedUploadedFile {
  name: string;
  type: string;
  size: number;
  section: string[];
  url?: string;
  file?: File;
  file_id?: number;
}

export interface FileUploadProps {
  onFileUpload: (file: UploadedFile) => void;
}

export interface FileDropdownProps {
  uploadedFiles: ExtendedUploadedFile[];
  selectedFile: ExtendedUploadedFile | null;
  selectFile: (file: ExtendedUploadedFile) => void;
  dropdownOpen: boolean;
  setDropdownOpen: (isOpen: boolean) => void;
}

export interface FileControlsProps {
  selectedFile: ExtendedUploadedFile | null;
  isLoading: boolean;
  handleSubmit: () => void;
  clearFileInput: () => void;
  folderType: 'personal' | 'project';
}

export interface TranscriptTabViewProps {
  activeTab: TranscriptTab;
  transcriptData: {
    transcript: string;
    summary: string;
    actionItems: string[];
    importantPoints: string[]; // Changed from assignments
    openQuestions: string[];
  };
}

export interface TranscriptTabNavigationProps {
  activeTab: TranscriptTab;
  setActiveTab: (tab: TranscriptTab) => void;
  availableTabs: TranscriptTab[];
  onSaveClick?: () => void;
  fileName: string;
  transcriptData: {
    transcript: string;
    summary: string;
    actionItems: string[];
    importantPoints: string[];
    openQuestions: string[];
  };
  isSharedView?: boolean;
}

interface SaveDialogProps {
  isOpen: boolean;
  onClose: () => void;
  fileName: string;
  transcriptData: TranscriptData;
  onSaveToPersonal: (fileName: string) => void;
  onSaveToProject: (fileName: string) => void;
}
