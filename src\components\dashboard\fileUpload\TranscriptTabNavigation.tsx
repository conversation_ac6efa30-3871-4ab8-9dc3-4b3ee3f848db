import React, { useState, useEffect } from 'react';
import { TranscriptTab } from '../models';
import { Button } from '@/components/ui/button';
import { Save, FolderInput, Upload } from 'lucide-react';
import { ExportDialog } from './ExportDialog';
import TrackerDialog from './TrackerDialog';
import { useAppSelector } from '@/hooks/useRedux';
import { TranscriptItem } from './types';

interface TranscriptTabNavigationProps {
  activeTab: TranscriptTab;
  setActiveTab: (tab: TranscriptTab) => void;
  availableTabs: TranscriptTab[];
  onSaveClick?: () => void;
  fileName: string;
  transcriptData: {
    transcript: TranscriptItem[];
    summary: string;
    actionItems: { action: string; dueDate: string; responsible: string }[];
    importantPoints: string[];
    openQuestions: { question: string }[];
  };
  isSharedView: boolean;
  selectedProject?: {
    id: string;
    name: string;
  };
  fileId?: number;
}

const tabLabels: Record<TranscriptTab, string> = {
  transcript: 'Transcript',
  summary: 'Summary',
  notes: 'Important Points',
  actionItems: 'Action Items',
  openQuestions: 'Open Questions'
};

const TranscriptTabNavigation: React.FC<TranscriptTabNavigationProps> = ({ 
  activeTab, 
  setActiveTab, 
  availableTabs,
  onSaveClick,
  fileName,
  transcriptData,
  isSharedView = false,
  selectedProject,
  fileId // Receive fileId
}) => {
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [isTrackerDialogOpen, setIsTrackerDialogOpen] = useState(false);
  const currentTranscript = useAppSelector(state => state.file.currentTranscript);

  return (
    <>
      <div className="sticky top-0 z-10 bg-white">
        <div className="flex flex-wrap md:flex-nowrap justify-between items-center p-4 border-b border-gray-200">
          <div className="flex flex-wrap gap-1 mb-2 md:mb-0">
            {availableTabs.map((tab) => (
              <button
                key={tab}
                className={`px-3 py-2 text-sm font-medium rounded-md ${
                  activeTab === tab
                    ? 'bg-green-100 text-green-800'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab(tab)}
              >
                {tabLabels[tab]}
              </button>
            ))}
          </div>
          
          {!isSharedView && (
            <div className="flex gap-2">
              <Button 
                onClick={() => setIsTrackerDialogOpen(true)}
                variant='greenmind'
                disabled={!selectedProject}
                title={!selectedProject ? "Please select a project first" : ""}
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload to Tracker
              </Button>
              <Button 
                onClick={() => setIsExportDialogOpen(true)}
                variant='white'
              >
                <FolderInput className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button 
                onClick={onSaveClick}
                variant='white'
                disabled={!onSaveClick}
              >
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>
          )}
        </div>
      </div>

      {!isSharedView && (
        <>
          <ExportDialog
            isOpen={isExportDialogOpen}
            onClose={() => setIsExportDialogOpen(false)}
            fileName={fileName}
            folderType={currentTranscript?.folderType || 'personal'}
            transcriptData={transcriptData}
          />
          
          <TrackerDialog
            isOpen={isTrackerDialogOpen}
            onClose={() => setIsTrackerDialogOpen(false)}
            fileName={fileName}
            selectedProject={selectedProject}
            fileId={fileId} // Pass fileId to TrackerDialog
          />
        </>
      )}
    </>
  );
};

export default TranscriptTabNavigation;
