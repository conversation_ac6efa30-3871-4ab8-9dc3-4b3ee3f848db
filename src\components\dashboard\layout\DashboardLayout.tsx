
import React from 'react';
import Header from '@/components/dashboard/Header';
import LeftSidebar from '@/components/dashboard/LeftSidebar';
import RightSidebar from '@/components/dashboard/RightSidebar';
import Content from '@/components/dashboard/Content';
import DashboardSettings from './DashboardSettings';
import DashboardContainer from './DashboardContainer';
import DashboardMain from './DashboardMain';
import DashboardContent from './DashboardContent';
import { useDashboardState } from '@/hooks/useDashboardState';
import MinimizedSidebar from '@/components/dashboard/MinimizedSidebar';

interface DashboardLayoutProps {
  initialDashboard?: 1 | 2 | 3;
  children?: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ initialDashboard = 1, children }) => {
  const {
    sidebarCollapsed,
    rightSidebarVisible,
    searchQuery, // This will be used only for main content
    headerSearchQuery,
    textSize,
    showMetricScreen,
    showDatasetScreen,
    activeDashboard,
    toggleSidebar,
    toggleRightSidebar,
    handleSearch, // This will only affect main content
    handleHeaderSearch,
    handleTextSizeChange,
    setShowMetricScreen,
    setShowDatasetScreen,
    getDashboardName
  } = useDashboardState(initialDashboard);

  const handleNewChat = () => {
    console.log("Creating new chat");
  };

  const handleDatasetIconClick = () => {
    setShowDatasetScreen(!showDatasetScreen);
    // Close metric screen if open
    if (showMetricScreen) {
      setShowMetricScreen(false);
    }
  };

  return (
    <>
      <DashboardSettings textSize={textSize} onTextSizeChange={handleTextSizeChange} />
      
      <DashboardContainer textSize={textSize}>
        <Header 
          toggleSidebar={toggleSidebar} 
          sidebarCollapsed={sidebarCollapsed}
          rightSidebarVisible={rightSidebarVisible}
          dashboardName={getDashboardName()}
          toggleRightSidebar={activeDashboard !== 2 ? toggleRightSidebar : undefined}
          onHeaderSearch={handleHeaderSearch}
          dashboardType={activeDashboard}
        />
        
        <DashboardMain>
          {/* Dashboard 2 specific rendering */}
          {activeDashboard === 2 ? (
            <div className="flex">
              {sidebarCollapsed ? (
                <MinimizedSidebar 
                  onPersonalFilesClick={() => toggleSidebar()}
                  onProjectFilesClick={() => toggleSidebar()}
                  isCollapsed={sidebarCollapsed}
                  onToggle={toggleSidebar}
                />
              ) : (
                <LeftSidebar 
                  collapsed={false} 
                  searchQuery={searchQuery}
                  handleSearch={handleSearch}
                  searchResults={[]}
                  onNewChat={handleNewChat}
                  onTextSizeChange={handleTextSizeChange}
                  dashboardType={activeDashboard}
                  onDatasetIconClick={handleDatasetIconClick}
                  isDatasetScreenOpen={showDatasetScreen}
                  onToggle={toggleSidebar}
                />
              )}
            </div>
          ) : (
            /* Dashboard 1 and 3 rendering */
            <div className="flex">
              <LeftSidebar 
                collapsed={sidebarCollapsed} 
                searchQuery="" // Pass empty string as we're using local state in LeftSidebar
                handleSearch={() => {}} // Pass empty handler as we're using local handler in LeftSidebar
                onTextSizeChange={handleTextSizeChange}
                dashboardType={activeDashboard}
                onDatasetIconClick={handleDatasetIconClick}
                isDatasetScreenOpen={showDatasetScreen}
                onToggle={toggleSidebar}
              />
            </div>
          )}
          
          <DashboardContent
            sidebarCollapsed={sidebarCollapsed}
            rightSidebarVisible={rightSidebarVisible}
          >
            <Content 
              dashboardType={activeDashboard} 
              textSize={textSize}
              searchQuery={searchQuery} // Main content search
              headerSearchQuery={headerSearchQuery}
              showMetricScreen={showMetricScreen} 
              showDatasetScreen={showDatasetScreen}
              setShowMetricScreen={setShowMetricScreen}
              setShowDatasetScreen={setShowDatasetScreen} 
            />
          </DashboardContent>
          
          {rightSidebarVisible && !showDatasetScreen && activeDashboard !== 2 && (
            <RightSidebar 
              dashboardType={activeDashboard} 
              toggleVisibility={toggleRightSidebar}
            />
          )}
        </DashboardMain>
      </DashboardContainer>
    </>
  );
};

export default DashboardLayout;
