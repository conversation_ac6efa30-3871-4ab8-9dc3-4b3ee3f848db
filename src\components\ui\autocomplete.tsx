import * as React from "react"
import { Check, ChevronUp, ChevronDown, X } from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"

export type AutocompleteOption = {
  value: string
  label: string
}

type AutocompleteProps = {
  options: AutocompleteOption[]
  placeholder?: string
  emptyMessage?: string
  value?: string | string[]
  onValueChange?: (value: string | string[]) => void
  disabled?: boolean
  multiple?: boolean
  maxItems?: number
  className?: string
  triggerClassName?: string
  contentClassName?: string
  searchPlaceholder?: string
}

export function Autocomplete({
  options,
  placeholder = "Select an option",
  emptyMessage = "No options found.",
  value,
  onValueChange,
  disabled = false,
  multiple = false,
  maxItems = 5,
  className,
  triggerClassName,
  contentClassName,
  searchPlaceholder = "Search..."
}: AutocompleteProps) {
  const [open, setOpen] = React.useState(false)
  const [selected, setSelected] = React.useState<string | string[]>(value || (multiple ? [] : ""))
  
  // Sync with external value
  React.useEffect(() => {
    if (value !== undefined) {
      setSelected(value)
    }
  }, [value])

  // Get selected option labels for display
  const getSelectedLabels = () => {
    if (!selected) return []
    
    const selectedArray = Array.isArray(selected) ? selected : [selected]
    return selectedArray
      .map(value => options.find(option => option.value === value))
      .filter(Boolean) as AutocompleteOption[]
  }

  const selectedLabels = getSelectedLabels()

  const handleSelect = (currentValue: string) => {
    let newValue: string | string[]
    
    if (multiple) {
      const selectedArray = Array.isArray(selected) ? selected : []
      if (selectedArray.includes(currentValue)) {
        newValue = selectedArray.filter(v => v !== currentValue)
      } else {
        newValue = [...selectedArray, currentValue]
      }
    } else {
      newValue = currentValue === selected ? "" : currentValue
      setOpen(false)
    }
    
    setSelected(newValue)
    onValueChange?.(newValue)
  }

  const handleRemove = (valueToRemove: string) => {
    if (!multiple) return
    
    const newValue = Array.isArray(selected) 
      ? selected.filter(v => v !== valueToRemove)
      : []
    
    setSelected(newValue)
    onValueChange?.(newValue)
  }

  const handleClear = () => {
    const newValue = multiple ? [] : ""
    setSelected(newValue)
    onValueChange?.(newValue)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            "w-full justify-between",
            selectedLabels.length > 0 && multiple ? "h-auto min-h-10 py-2" : "h-10",
            triggerClassName
          )}
          onClick={() => setOpen(!open)}
        >
          {selectedLabels.length > 0 ? (
            multiple ? (
              <div className="flex flex-wrap gap-1 mr-2">
                {selectedLabels.map(option => (
                  <Badge 
                    key={option.value} 
                    variant="secondary"
                    className="flex items-center gap-1 px-2 py-0"
                  >
                    {option.label}
                    <button
                      className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleRemove(option.value)
                        }
                      }}
                      onMouseDown={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                      }}
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        handleRemove(option.value)
                      }}
                    >
                      <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                    </button>
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-between w-full">
                <span>{selectedLabels[0]?.label}</span>
                {selectedLabels.length > 0 && (
                  <X 
                    className="h-4 w-4 opacity-50 hover:opacity-100 ml-2"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleClear()
                    }}
                  />
                )}
              </div>
            )
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          {open ? (
            <ChevronUp className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          ) : (
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className={cn("p-0 w-[var(--radix-popover-trigger-width)]", contentClassName)}>
        <Command className={className}>
          <div className="flex items-center border-b px-3">
            <CommandInput placeholder={searchPlaceholder} className="h-9" />
          </div>
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              <ScrollArea className={cn("max-h-[200px]", maxItems > 5 && "max-h-[300px]")}>
                {options.map((option) => {
                  const isSelected = multiple 
                    ? Array.isArray(selected) && selected.includes(option.value)
                    : selected === option.value
                  
                  return (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={handleSelect}
                    >
                      <div className="flex items-center">
                        {multiple && (
                          <div className={cn(
                            "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                            isSelected ? "bg-primary text-primary-foreground" : "opacity-50"
                          )}>
                            {isSelected && <Check className="h-3 w-3" />}
                          </div>
                        )}
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            isSelected ? "opacity-100" : "opacity-0",
                            multiple && "hidden"
                          )}
                        />
                        {option.label}
                      </div>
                    </CommandItem>
                  )
                })}
              </ScrollArea>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
