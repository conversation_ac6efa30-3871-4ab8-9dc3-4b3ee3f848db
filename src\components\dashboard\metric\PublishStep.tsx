
import React from "react"
import { Check } from "lucide-react"

export const PublishStep: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-6">
        <Check className="h-12 w-12 text-green-600" />
      </div>
      <h3 className="text-xl font-medium text-gray-900 mb-2">Metric Published Successfully</h3>
      <p className="text-center text-gray-600 mb-6">
        Your metric has been published and is now available for use across the platform.
      </p>
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 max-w-lg">
        <h4 className="text-blue-800 font-medium mb-2">What happens next?</h4>
        <ul className="list-disc pl-5 text-blue-700 space-y-1">
          <li>The metric will be available in dashboards and reports</li>
          <li>Users with appropriate permissions can view and use this metric</li>
          <li>You can edit or update this metric from the metrics management page</li>
        </ul>
      </div>
    </div>
  )
}
