
import React from 'react';
import { Virtuoso } from 'react-virtuoso';
import { FolderType } from '@/services/api/audioTranscript/types';
import FileItem from './FileItem';

interface VirtualizedFileListProps {
  files: string[];
  folderType: FolderType;
  onFileClick: (fileName: string, folderType: FolderType) => void;
  onDeleteClick: (fileName: string, folderType: FolderType) => void;
  isTracker?: boolean;
}

const VirtualizedFileList: React.FC<VirtualizedFileListProps> = ({
  files,
  folderType,
  onFileClick,
  onDeleteClick,
  isTracker = false
}) => {
  // Calculate dynamic height based on number of files
  const listHeight = Math.min(files.length * 32, 300); // 32px per item, max 300px

  return (
    <div style={{ height: files.length > 0 ? listHeight : 'auto' }} className="overflow-auto">
      <Virtuoso
        totalCount={files.length}
        itemContent={index => (
          <FileItem
            key={files[index]}
            fileName={files[index]}
            onFileClick={() => onFileClick(files[index], folderType)}
            onDeleteClick={(e) => {
              e.stopPropagation();
              onDeleteClick(files[index], folderType);
            }}
            isTracker={isTracker}
          />
        )}
        style={{ height: '100%' }}
        className="file-list-virtuoso"
        increaseViewportBy={100}
      />
    </div>
  );
};

export default VirtualizedFileList;
