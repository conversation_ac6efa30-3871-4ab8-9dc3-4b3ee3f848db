
/* Remove all existing tracker-related styles and start fresh */

.file-list-virtuoso::-webkit-scrollbar {
  width: 6px;
}

.file-list-virtuoso::-webkit-scrollbar-track {
  background: transparent;
}

.file-list-virtuoso::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.file-list-virtuoso::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Clean scrollbar styles for better consistency */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
  border: 1px solid #f1f5f9;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Firefox scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .tracker-container {
    height: calc(100vh - 56px);
  }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .tracker-container {
    height: calc(100vh - 60px);
  }
}

/* Desktop */
@media (min-width: 1025px) {
  .tracker-container {
    height: calc(100vh - 64px);
  }
}
