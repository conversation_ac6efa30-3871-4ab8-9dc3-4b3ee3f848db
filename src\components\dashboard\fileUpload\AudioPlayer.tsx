
import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { SkipBack, Play, Pause, SkipForward, Volume2, Maximize } from 'lucide-react';
import { TranscriptItem } from './types';

interface AudioPlayerProps {
  audioUrl: string;
  transcript: TranscriptItem[];
  onTimeUpdate: (currentTime: number) => void;
  onLoadStart?: () => void;
  onCanPlay?: () => void;
}

const AudioPlayer = forwardRef<HTMLAudioElement, AudioPlayerProps>(({ audioUrl, transcript, onTimeUpdate, onLoadStart, onCanPlay }, ref) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [playbackRate, setPlaybackRate] = useState(1);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Expose the audio element ref to parent components
  useImperativeHandle(ref, () => audioRef.current as HTMLAudioElement);

  // Log when audio URL changes
  useEffect(() => {
    // console.log("AudioPlayer: Audio URL updated:", audioUrl);
  }, [audioUrl]);

  // Set up main event listeners for the audio element
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
      if (onTimeUpdate) {
        onTimeUpdate(audio.currentTime);
      }
    };

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      // console.log("Audio metadata loaded. Duration:", audio.duration);
    };

    const handleEnded = () => {
      setIsPlaying(false);
    };

    const handlePlay = () => {
      setIsPlaying(true);
    };

    const handlePause = () => {
      setIsPlaying(false);
      if (onTimeUpdate) {
        // Call time update on pause to ensure highlighting remains
        onTimeUpdate(audio.currentTime);
      }
    };

    // Add all event listeners
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    
    // Log audio element status
    // console.log("Audio element initialized with URL:", audio.src);

    // Cleanup all listeners when component unmounts or audio changes
    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
    };
  }, [onTimeUpdate, audioUrl]); // Re-setup when URL changes

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play().catch(err => {
        // console.error("Error playing audio:", err);
      });
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = parseFloat(e.target.value);
    audio.currentTime = newTime;
    setCurrentTime(newTime);
    if (onTimeUpdate) {
      onTimeUpdate(newTime);
    }
  };

  const skipBackward = () => {
    const audio = audioRef.current;
    if (!audio) return;
    
    const newTime = Math.max(0, audio.currentTime - 10);
    audio.currentTime = newTime;
    setCurrentTime(newTime);
    if (onTimeUpdate) {
      onTimeUpdate(newTime);
    }
  };

  const skipForward = () => {
    const audio = audioRef.current;
    if (!audio) return;
    
    const newTime = Math.min(duration, audio.currentTime + 10);
    audio.currentTime = newTime;
    setCurrentTime(newTime);
    if (onTimeUpdate) {
      onTimeUpdate(newTime);
    }
  };

  const changePlaybackRate = () => {
    const audio = audioRef.current;
    if (!audio) return;
    
    const rates = [0.5, 0.75, 1, 1.25, 1.5, 2];
    const currentIndex = rates.indexOf(playbackRate);
    const nextIndex = (currentIndex + 1) % rates.length;
    const newRate = rates[nextIndex];
    
    audio.playbackRate = newRate;
    setPlaybackRate(newRate);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  useEffect(() => {
    if (audioRef.current) {
      const audioElement = audioRef.current;
      
      if (onLoadStart) {
        audioElement.addEventListener('loadstart', onLoadStart);
      }
      
      if (onCanPlay) {
        audioElement.addEventListener('canplay', onCanPlay);
      }
      
      return () => {
        if (onLoadStart) {
          audioElement.removeEventListener('loadstart', onLoadStart);
        }
        
        if (onCanPlay) {
          audioElement.removeEventListener('canplay', onCanPlay);
        }
      };
    }
  }, [onLoadStart, onCanPlay]);

  return (
    <div className="sticky bottom-0 bg-white border-t border-gray-200 p-2 shadow-md z-50 w-full">
      <audio 
        ref={audioRef} 
        src={audioUrl} 
        preload="auto" 
        // onError={(e) => console.error("Audio loading error:", e)}
      />
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <button 
            onClick={skipBackward}
            className="p-2 rounded-full hover:bg-gray-100"
            title="Skip back 10 seconds"
          >
            <SkipBack size={18} />
          </button>
          
          <button 
            onClick={togglePlayPause}
            className="p-2 rounded-full bg-blue-500 text-white hover:bg-blue-600"
          >
            {isPlaying ? <Pause size={18} /> : <Play size={18} />}
          </button>
          
          <button 
            onClick={skipForward}
            className="p-2 rounded-full hover:bg-gray-100"
            title="Skip forward 10 seconds"
          >
            <SkipForward size={18} />
          </button>
        </div>
        
        <div className="flex-1 mx-4">
          <div className="flex items-center">
            <span className="text-xs text-gray-500 w-10">{formatTime(currentTime)}</span>
            <input
              type="range"
              min="0"
              max={duration || 100}
              value={currentTime}
              onChange={handleSeek}
              className="flex-1 mx-2 h-1 bg-gray-300 rounded-full appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, #3b82f6 ${(currentTime / duration) * 100}%, #e5e7eb ${(currentTime / duration) * 100}%)`
              }}
            />
            <span className="text-xs text-gray-500 w-10">{formatTime(duration)}</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button 
            onClick={changePlaybackRate}
            className="px-2 py-1 text-xs rounded border border-gray-300 hover:bg-gray-100"
            title="Change playback speed"
          >
            {playbackRate}x
          </button>
          
          <button className="p-2 rounded-full hover:bg-gray-100" title="Volume">
            <Volume2 size={18} />
          </button>
          
          <button className="p-2 rounded-full hover:bg-gray-100" title="Fullscreen">
            <Maximize size={18} />
          </button>
        </div>
      </div>
    </div>
  );
});

export default AudioPlayer;
