import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  DialogHeader, 
  <PERSON>alogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { getChangeHistory, ChangeHistoryItem } from '@/services/api/audioTranscript/trackerService';
import { toast } from 'sonner';

interface ChangeHistoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  trackerId: string | null;
  activityName: string;
}

export function ChangeHistoryDialog({ 
  isOpen, 
  onClose, 
  trackerId,
  activityName
}: ChangeHistoryDialogProps) {
  const [history, setHistory] = useState<ChangeHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Debug log to verify dialog is being triggered
  useEffect(() => {
    if (isOpen) {
      console.log(`ChangeHistoryDialog opened for tracker ID: ${trackerId}`);
    }
  }, [isOpen, trackerId]);

  useEffect(() => {
    if (isOpen && trackerId) {
      fetchChangeHistory(trackerId);
    }
  }, [isOpen, trackerId]);

  const fetchChangeHistory = async (id: string) => {
    setIsLoading(true);
    try {
      console.log(`Fetching change history for tracker ID: ${id}`);
      const historyData = await getChangeHistory(id);
      console.log(`Received ${historyData.length} history items`);
      setHistory(historyData);
    } catch (error) {
      console.error('Failed to fetch change history:', error);
      toast.error('Failed to load change history');
    } finally {
      setIsLoading(false);
    }
  };

  // Format date helper function
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd-MM-yyyy');
    } catch (e) {
      return dateString;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="dialog-content max-w-4xl">
        <DialogHeader>
          <DialogTitle>Change History for: {activityName}</DialogTitle>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading history...</span>
          </div>
        ) : history.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No change history available
          </div>
        ) : (
          <ScrollArea className="h-[60vh] w-full" orientation="both">
            <div className="min-w-max">
              <Table>
                <TableHeader className="sticky top-0 z-10">
                  <TableRow className="bg-teal-600 text-white hover:bg-teal-600">
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Project Name</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Activity</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Activity Type</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Assigned On</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Activity Status</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Assigned To</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">ETA</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Meeting ID</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Comments</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Tags</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Modified On</TableHead>
                    <TableHead className="border border-gray-300 px-2 py-1.5 text-left font-medium h-auto text-white">Modified By</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {history.map((item, index) => (
                    <TableRow key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.new_values.project_name || ''}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.new_values.activity || ''}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.new_values.activity_type || ''}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.new_values.assigned_on ? formatDate(item.new_values.assigned_on) : ''}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.new_values.activity_status || ''}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.new_values.assigned_to || ''}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.new_values.eta ? formatDate(item.new_values.eta) : ''}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.new_values.meeting_id || ''}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.new_values.comments || ''}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.new_values.tags || ''}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {formatDate(item.modifiedOn)}
                      </TableCell>
                      <TableCell className="border border-gray-300 px-2 py-1.5">
                        {item.modifiedBy}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </ScrollArea>
        )}
        
        <DialogFooter>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}




