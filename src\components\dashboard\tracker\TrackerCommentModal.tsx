
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { format } from "date-fns";
import { CommentItem } from '@/hooks/useTrackerItems';

interface CommentModalProps { 
  isOpen: boolean;
  onClose: () => void;
  commentItems?: CommentItem[];
  onAddComment: () => void;
  newCommentText: string;
  setNewCommentText: (text: string) => void;
  userInitials: string;
}

const TrackerCommentModal: React.FC<CommentModalProps> = ({ 
  isOpen, 
  onClose, 
  commentItems, 
  onAddComment, 
  newCommentText, 
  setNewCommentText,
  userInitials
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="absolute inset-0 bg-black bg-opacity-25" onClick={onClose}></div>
      <div className="bg-white rounded-md shadow-lg w-96 z-10">
        <div className="p-2 bg-blue-50 text-blue-800 text-xs font-medium border-b flex justify-between items-center">
          <span>Comments</span>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            &times;
          </button>
        </div>
        
        <div className="max-h-80 overflow-y-auto p-3">
          {(commentItems && commentItems.length > 0) ? (
            commentItems.map((comment) => (
              <div key={comment.id} className="mb-4">
                <div className="flex items-start gap-2">
                  <Avatar className="h-8 w-8 bg-blue-500">
                    <AvatarFallback>{comment.userInitials}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex justify-between">
                      <div className="font-medium text-sm">{comment.user}</div>
                      <div className="text-xs text-gray-500">
                        {format(new Date(comment.timestamp), 'MMM d, yyyy, h:mm a')}
                      </div>
                    </div>
                    <div className="text-sm mt-1">{comment.text}</div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center text-gray-500 py-4">
              No comments yet. Add one below.
            </div>
          )}
        </div>
        
        <div className="p-3 border-t">
          <div className="flex items-start gap-2">
            <Avatar className="h-8 w-8 bg-blue-500">
              <AvatarFallback>{userInitials}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <input
                type="text"
                value={newCommentText}
                onChange={(e) => setNewCommentText(e.target.value)}
                placeholder="@mention or reply"
                className="w-full p-2 border rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    onAddComment();
                  }
                }}
              />
              <div className="flex justify-end mt-2">
                <Button 
                  size="sm" 
                  className="text-xs bg-blue-500 hover:bg-blue-600 text-white"
                  onClick={onAddComment}
                  disabled={!newCommentText.trim()}
                >
                  Add
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrackerCommentModal;
