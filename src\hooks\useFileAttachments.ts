
import { useState } from 'react';

interface UseFileAttachmentsReturn {
  files: File[];
  isAttachmentDialogOpen: boolean;
  setIsAttachmentDialogOpen: (isOpen: boolean) => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  removeFile: (index: number) => void;
}

export const useFileAttachments = (): UseFileAttachmentsReturn => {
  const [files, setFiles] = useState<File[]>([]);
  const [isAttachmentDialogOpen, setIsAttachmentDialogOpen] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setFiles((prevFiles) => [...prevFiles, ...newFiles]);
      setIsAttachmentDialogOpen(false);
    }
  };

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  return {
    files,
    isAttachmentDialogOpen,
    setIsAttachmentDialogOpen,
    handleFileChange,
    removeFile
  };
};
