
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useLocation,Outlet } from "react-router-dom";
import { Provider } from "react-redux";
import { store } from "@/stores/store";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import NotFound from "./pages/NotFound";
import SignIn from "./pages/SignIn";
import SignUp from "./pages/SignUp";
import { AuthProvider } from "./contexts/AuthContext";
import { useAuth } from "./contexts/AuthContext";
import ErrorDialog from "./components/ui/ErrorDialog";
import SharedTranscriptView from './pages/SharedTranscriptView';
import AdminLayout from '@/components/admin/AdminLayout';
import AdminMenu from '@/pages/admin/AdminMenu';
import FileUploadView from './components/dashboard/fileUpload/FileUploadView';
import TrackerTableView from './components/dashboard/tracker/TrackerTableView';
import AzureLogout from './components/auth/AzureLogout';
import ProjectDetails from './components/admin/ProjectDetails';

// Create route guard component
const ProtectedRoute = ({ 
  children, 
  requiredFeature,
  isSharedRoute = false
}: { 
  children: React.ReactNode, 
  requiredFeature?: 'chatbot' | 'transcript' | 'dada',
  isSharedRoute?: boolean
}) => {
  const { isAuthenticated, hasAccess } = useAuth();
  const location = useLocation();
  
  // Check localStorage for valid session
  const hasValidSession = () => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      const userData = JSON.parse(storedUser);
      const currentTime = new Date().getTime();
      return userData.expiryTime && userData.expiryTime > currentTime;
    }
    return false;
  };
  
  const isUserAuthenticated = isAuthenticated || hasValidSession();
  
  // For shared routes, check authentication
  if (isSharedRoute && isUserAuthenticated) {
    return <>{children}</>;
  }
  
  // For non-authenticated users, redirect to signin
  if (!isUserAuthenticated) {
    return <Navigate to="/signin" state={{ from: location }} replace />;
  }
  
  // For regular routes, check feature access
  if (requiredFeature && !hasAccess(requiredFeature)) {
    return <Navigate to="/" replace />;
  }
  
  return <>{children}</>;
};

// Wrap the ProtectedRoute in a component that doesn't use hooks
const RoutesWithAuth = () => {
  return (
    <Routes>
      <Route path="/" element={<Index />} />
      <Route path="/signin" element={<SignIn />} />
      <Route path="/signup" element={<SignUp />} />
      
      {/* Protected Routes */}
      <Route 
        path="/chatbot" 
        element={
          <ProtectedRoute requiredFeature="chatbot">
            <Dashboard initialDashboard={1} />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/transcript" 
        element={
          <ProtectedRoute requiredFeature="transcript">
            <Dashboard initialDashboard={2} />
          </ProtectedRoute>
        }
      >
        <Route index element={<Outlet />} />
        <Route path="tracker/:projectName" element={<TrackerTableView />} />
      </Route>
      <Route 
        path="/transcript/tracker/:projectName" 
        element={
          <ProtectedRoute requiredFeature="transcript">
            <Dashboard initialDashboard={2} />
          </ProtectedRoute>
        }
      />
      <Route 
        path="/transcript/:folderType/:fileName" 
        element={
          <ProtectedRoute requiredFeature="transcript" isSharedRoute={false}>
            <Dashboard />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/transcript/:folderType/:fileName/sharedview" 
        element={
          <ProtectedRoute isSharedRoute={true}>
            <SharedTranscriptView />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/dada" 
        element={
          <ProtectedRoute requiredFeature="dada">
            <Dashboard initialDashboard={3} />
          </ProtectedRoute>
        } 
      />
      
      {/* Legacy route - redirect to the appropriate dashboard */}
      <Route path="/dashboard" element={<Navigate to="/chatbot" replace />} />
      
      {/* 404 route */}
      <Route path="*" element={<NotFound />} />
      
      {/* Admin Routes */}
      <Route 
        path="/Admin" 
        element={<AdminLayout />}
      >
        <Route path="menu" element={<AdminMenu />} />
        <Route path="project/:projectId" element={<ProjectDetails />} />
      </Route>
      <Route 
        path="/azure-logout" 
        element={<AzureLogout />} 
      />
    </Routes>
  );
};

const queryClient = new QueryClient();

const App = () => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <ErrorDialog />
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthProvider>
            <RoutesWithAuth />
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  </Provider>
);

export default App;
