
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Add TrackerFile interface
interface TrackerFile {
  fileName: string;
  projectId: string;
  projectName: string;
}

interface TranscriptResponse {
  transcript: string;
  file_id: number;
  summary: {
    summary: string;
    actionItems: string[];
    importantPoints: string[];
    openQuestions: string[];
  };
}

interface CurrentTranscript {
  data: TranscriptResponse | null;
  fileName: string | null;
  folderType: 'personal' | 'project' | null;
  isExisting: boolean;
}

interface CacheTimestamps {
  personalFiles: number;
  projectFiles: number;
  trackerProjects: number;
}

interface FileState {
  personalFiles: string[];
  projectFiles: string[];
  trackerFiles: TrackerFile[];
  trackerProjects: { id: string; name: string; fileCount: number }[];
  isPersonalExpanded: boolean;
  isProjectExpanded: boolean;
  isTrackerExpanded: boolean;
  currentTranscript: CurrentTranscript | null;
  lastFetched: CacheTimestamps;
  fetchStatus: {
    personalFiles: 'idle' | 'loading' | 'succeeded' | 'failed';
    projectFiles: 'idle' | 'loading' | 'succeeded' | 'failed';
    trackerProjects: 'idle' | 'loading' | 'succeeded' | 'failed';
  };
}

const CACHE_TTL = 60 * 60 * 1000; // 1 hour cache TTL

const initialState: FileState = {
  personalFiles: [],
  projectFiles: [],
  trackerFiles: [],
  trackerProjects: [],
  isPersonalExpanded: true,
  isProjectExpanded: true,
  isTrackerExpanded: true,
  currentTranscript: null,
  lastFetched: {
    personalFiles: 0,
    projectFiles: 0,
    trackerProjects: 0
  },
  fetchStatus: {
    personalFiles: 'idle',
    projectFiles: 'idle',
    trackerProjects: 'idle'
  }
};

const fileSlice = createSlice({
  name: 'file',
  initialState,
  reducers: {
    setPersonalFiles: (state, action: PayloadAction<string[]>) => {
      state.personalFiles = action.payload;
      state.lastFetched.personalFiles = Date.now();
      state.fetchStatus.personalFiles = 'succeeded';
    },
    setProjectFiles: (state, action: PayloadAction<string[]>) => {
      state.projectFiles = action.payload;
      state.lastFetched.projectFiles = Date.now();
      state.fetchStatus.projectFiles = 'succeeded';
    },
    setTrackerProjects: (state, action: PayloadAction<Array<{id: string; name: string; fileCount: number}>>) => {
      state.trackerProjects = action.payload;
      state.lastFetched.trackerProjects = Date.now();
      state.fetchStatus.trackerProjects = 'succeeded';
    },
    setFetchStatus: (state, action: PayloadAction<{
      type: 'personalFiles' | 'projectFiles' | 'trackerProjects';
      status: 'idle' | 'loading' | 'succeeded' | 'failed';
    }>) => {
      state.fetchStatus[action.payload.type] = action.payload.status;
    },
    togglePersonalExpanded: (state) => {
      state.isPersonalExpanded = !state.isPersonalExpanded;
    },
    toggleProjectExpanded: (state) => {
      state.isProjectExpanded = !state.isProjectExpanded;
    },
    setCurrentTranscript: (state, action: PayloadAction<{
      data: any;
      fileName: string;
      folderType: 'personal' | 'project';
      isExisting: boolean;
    }>) => {
      state.currentTranscript = action.payload;
      // Add debug log
      console.log('Redux: Setting current transcript with folder type:', action.payload.folderType);
    },
    clearCurrentTranscript: (state) => {
      state.currentTranscript = initialState.currentTranscript;
    },
    addToPersonal: (state, action: PayloadAction<string>) => {
      if (!state.personalFiles.includes(action.payload)) {
        state.personalFiles.push(action.payload);
      }
    },
    addToProject: (state, action: PayloadAction<string>) => {
      if (!state.projectFiles.includes(action.payload)) {
        state.projectFiles.push(action.payload);
      }
    },
    // Add reducer for tracker files
    addToTracker: (state, action: PayloadAction<TrackerFile>) => {
      const exists = state.trackerFiles.some(
        file => file.projectId === action.payload.projectId && file.fileName === action.payload.fileName
      );
      
      if (!exists) {
        state.trackerFiles.push(action.payload);
      }

      // Update the project's file count in trackerProjects
      const projectIndex = state.trackerProjects.findIndex(
        project => project.id === action.payload.projectId || project.name === action.payload.projectName
      );

      if (projectIndex >= 0) {
        state.trackerProjects[projectIndex].fileCount++;
      } else {
        state.trackerProjects.push({
          id: action.payload.projectId,
          name: action.payload.projectName,
          fileCount: 1
        });
      }
    },
    removeFromTracker: (state, action: PayloadAction<{ projectId: string, fileName: string }>) => {
      state.trackerFiles = state.trackerFiles.filter(
        file => !(file.projectId === action.payload.projectId && file.fileName === action.payload.fileName)
      );
      
      // Update file count
      const projectIndex = state.trackerProjects.findIndex(
        project => project.id === action.payload.projectId
      );

      if (projectIndex >= 0) {
        state.trackerProjects[projectIndex].fileCount = Math.max(0, state.trackerProjects[projectIndex].fileCount - 1);
      }
    },
    toggleTrackerExpanded: (state) => {
      state.isTrackerExpanded = !state.isTrackerExpanded;
    },
    invalidateCache: (state, action: PayloadAction<'personalFiles' | 'projectFiles' | 'trackerProjects' | 'all'>) => {
      if (action.payload === 'all') {
        state.lastFetched = initialState.lastFetched;
      } else {
        state.lastFetched[action.payload] = 0;
      }
    },
    // Optional: Manually clear cache after 24 hours if needed
    clearStaleCache: (state) => {
      const now = Date.now();
      const dayInMs = 24 * 60 * 60 * 1000;
      
      if (now - state.lastFetched.personalFiles > dayInMs) {
        state.lastFetched.personalFiles = 0;
      }
      
      if (now - state.lastFetched.projectFiles > dayInMs) {
        state.lastFetched.projectFiles = 0;
      }
      
      if (now - state.lastFetched.trackerProjects > dayInMs) {
        state.lastFetched.trackerProjects = 0;
      }
    }
  },
});

export const {
  setPersonalFiles,
  setProjectFiles,
  setTrackerProjects,
  setFetchStatus,
  togglePersonalExpanded,
  toggleProjectExpanded,
  toggleTrackerExpanded,
  setCurrentTranscript,
  clearCurrentTranscript,
  addToPersonal,
  addToProject,
  addToTracker,
  removeFromTracker,
  invalidateCache,
  clearStaleCache
} = fileSlice.actions;

// Create a selector for checking if data is stale
export const selectIsCacheValid = (state: { file: FileState }, cacheType: 'personalFiles' | 'projectFiles' | 'trackerProjects') => {
  const lastFetched = state.file.lastFetched[cacheType];
  return Date.now() - lastFetched < CACHE_TTL;
};

export default fileSlice.reducer;
