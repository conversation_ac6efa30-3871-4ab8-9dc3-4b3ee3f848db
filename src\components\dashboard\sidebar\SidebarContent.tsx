
import React, { useEffect, useCallback } from 'react';
import { useAppSelector, useAppDispatch } from '@/hooks/useRedux';
import { useFileManagement } from '@/hooks/useFileManagement';
import { useDeleteDialog } from '@/hooks/useDeleteDialog';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';
import LoadingOverlay from './LoadingOverlay';
import FilesDashboardView from './views/FilesDashboardView';
import ListDashboardView from './views/ListDashboardView';
import { ChatTopic, Meeting, Project } from './types';
import { togglePersonalExpanded, toggleProjectExpanded, toggleTrackerExpanded } from '@/stores/fileSlice';

interface SidebarContentProps {
  collapsed: boolean;
  searchQuery: string;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  filteredItems: ChatTopic[] | Meeting[] | Project[];
  toggleItem: (id: string) => void;
  addChatToItem: (id: string) => void;
  addNewItem: () => void;
  dashboardType: 1 | 2 | 3;
  onToggle: () => void;
}

const SidebarContent: React.FC<SidebarContentProps> = ({
  collapsed,
  searchQuery,
  handleSearch,
  filteredItems,
  toggleItem,
  addChatToItem,
  addNewItem,
  dashboardType,
  onToggle
}) => {
  const {
    personalFiles,
    projectFiles,
    isPersonalExpanded,
    isProjectExpanded
  } = useAppSelector(state => state.file);

  const {
    isLoading,
    isLoadingFiles,
    fetchFiles,
    handleFileClick,
    fileData
  } = useFileManagement(dashboardType);

  const {
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    fileToDelete,
    deleteStatus,
    handleDeleteClick,
    handleDelete
  } = useDeleteDialog(personalFiles, projectFiles, window.clearFileInput);

  const dispatch = useAppDispatch();

  // Only fetch files once when mounting Dashboard Type 2
  useEffect(() => {
    let isMounted = true;
    
    if (isMounted && dashboardType === 2) {
      // This will use cache if valid, only fetch from API if needed
      fetchFiles();
    }
    
    return () => {
      isMounted = false;
    };
  }, [dashboardType, fetchFiles]);

  // Memoized toggle functions to prevent unnecessary re-renders
  const handleTogglePersonalExpanded = useCallback(() => {
    dispatch(togglePersonalExpanded());
  }, [dispatch]);

  const handleToggleProjectExpanded = useCallback(() => {
    dispatch(toggleProjectExpanded());
  }, [dispatch]);
  
  const handleToggleTrackerExpanded = useCallback(() => {
    dispatch(toggleTrackerExpanded());
  }, [dispatch]);

  if (collapsed) {
    return null;
  }

  if (dashboardType === 2) {
    return (
      <>
        <LoadingOverlay isVisible={isLoading} />
        <FilesDashboardView
          searchQuery={searchQuery}
          handleSearch={handleSearch}
          personalFiles={personalFiles}
          projectFiles={projectFiles}
          isPersonalExpanded={isPersonalExpanded}
          isProjectExpanded={isProjectExpanded}
          isLoadingFiles={isLoadingFiles}
          handleFileClick={handleFileClick}
          handleDeleteClick={handleDeleteClick}
          togglePersonalExpanded={handleTogglePersonalExpanded}
          toggleProjectExpanded={handleToggleProjectExpanded}
          onToggle={onToggle}
        />
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onClose={() => setIsDeleteDialogOpen(false)}
          fileName={fileToDelete?.fileName || null}
          onConfirm={handleDelete}
          status={deleteStatus}
        />
      </>
    );
  }

  return (
    <>
      <LoadingOverlay isVisible={isLoading} />
      <ListDashboardView
        searchQuery={searchQuery}
        handleSearch={(e) => {
          // Only update sidebar search, don't affect main content
          handleSearch(e);
        }}
        filteredItems={filteredItems}
        toggleItem={toggleItem}
        addChatToItem={addChatToItem}
        addNewItem={addNewItem}
        dashboardType={dashboardType}
        onToggle={onToggle}
      />
    </>
  );
};

export default React.memo(SidebarContent);
