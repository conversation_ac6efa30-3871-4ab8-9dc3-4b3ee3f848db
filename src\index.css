
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* postcss-ignore-next-line */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    
    --radius: 0.75rem;
  }
 
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    font-family: 'Inter', sans-serif;
    scroll-behavior: smooth;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }

  h1 {
    @apply text-4xl sm:text-5xl md:text-6xl;
  }

  h2 {
    @apply text-3xl sm:text-4xl;
  }
  
  h3 {
    @apply text-2xl sm:text-3xl;
  }

  .glass {
    @apply bg-white/5 backdrop-blur-lg border border-white/10 shadow-xl;
  }

  .section {
    @apply py-16 md:py-24;
  }
}

/* Custom utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500;
  }
}

/* Custom scrollbar styles */
textarea::-webkit-scrollbar {
  width: 6px;
}

textarea::-webkit-scrollbar-track {
  background: transparent;
}

textarea::-webkit-scrollbar-thumb {
  background-color: #CBD5E1;
  border-radius: 3px;
}

/* Firefox */
textarea {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E1 transparent;
}

/* Add this at the end of the file to fix the double scrollbar issue */
html, body, #root {
  height: 100%;
  overflow: hidden;
}

/* This ensures only the main content area scrolls */
.admin-content {
  overflow-y: auto;
  height: 100%;
}

/* Add this class for the hover-scrollbar functionality */
.hover-scrollbar {
  overflow-y: auto;
}

/* Custom scrollbar styles for all scrollable areas */
.hover-scrollbar::-webkit-scrollbar,
.border.rounded-md.max-h-40.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
  height: 6px;
}

.hover-scrollbar::-webkit-scrollbar-track,
.border.rounded-md.max-h-40.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.hover-scrollbar::-webkit-scrollbar-thumb,
.border.rounded-md.max-h-40.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #b9c1d1;
  border-radius: 5px;
}

/* Make sure this class is applied to the main content area */
.admin-content {
  overflow-y: auto;
  height: 100%;
}

/* Add a custom scrollbar class with smaller height */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E1 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #CBD5E1;
  border-radius: 2px;
}

/* Dialog scrollbar styles */
.DialogContent {
  max-height: 85vh;
  overflow-y: auto;
}

.DialogContent::-webkit-scrollbar {
  width: 4px;
}

.DialogContent::-webkit-scrollbar-track {
  background: transparent;
}

.DialogContent::-webkit-scrollbar-thumb {
  background-color: #CBD5E1;
  border-radius: 3px;
}

/* Add this class to force scrollbars to be always visible */
.custom-scrollbar-always-visible::-webkit-scrollbar {
  width: 8px;
  height: 8px; /* Height for horizontal scrollbar */
  display: block !important;
  visibility: visible !important;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* For Firefox */
.custom-scrollbar-always-visible {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* Clean scrollbar styles for better consistency */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
  border: 1px solid #f1f5f9;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Firefox scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;
}

/* Custom scrollbar that's always visible */
.custom-scrollbar-always-visible::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: block !important;
  visibility: visible !important;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* For Firefox */
.custom-scrollbar-always-visible {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* Ensure the table takes up available space */
.tracker-table-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

/* Ensure the table has proper scrolling */
.tracker-table-container > div:nth-child(2) {
  flex: 1;
  overflow: hidden;
}

/* Ensure the table header is sticky */
.tracker-table-container > div:nth-child(1) {
  position: sticky;
  top: 0;
  z-index: 20;
  background-color: white;
}

/* Ensure the table footer is sticky */
.tracker-table-container > div:nth-child(3) {
  position: sticky;
  bottom: 0;
  z-index: 20;
  background-color: white;
}

/* Remove all existing tracker-related styles and start fresh */

/* Clean scrollbar styles for better consistency */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
  border: 1px solid #f1f5f9;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Firefox scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;
}

/* Custom scrollbar that's always visible */
.custom-scrollbar-always-visible::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: block !important;
  visibility: visible !important;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* For Firefox */
.custom-scrollbar-always-visible {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* Ensure the table takes up available space */
.tracker-table-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

/* Ensure the table has proper scrolling */
.tracker-table-container > div:nth-child(2) {
  flex: 1;
  overflow: hidden;
}

/* Ensure the table header is sticky */
.tracker-table-container > div:nth-child(1) {
  position: sticky;
  top: 0;
  z-index: 20;
  background-color: white;
}

/* Ensure the table footer is sticky */
.tracker-table-container > div:nth-child(3) {
  position: sticky;
  bottom: 0;
  z-index: 20;
  background-color: white;
}

/* Remove all existing tracker-related styles and start fresh */

/* Clean scrollbar styles for better consistency */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
  border: 1px solid #f1f5f9;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Firefox scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;
}

/* Custom scrollbar that's always visible */
.custom-scrollbar-always-visible::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: block !important;
  visibility: visible !important;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.custom-scrollbar-always-visible::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* For Firefox */
.custom-scrollbar-always-visible {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* Ensure the table takes up available space */
.tracker-table-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

/* Ensure the table has proper scrolling */
.tracker-table-container > div:nth-child(2) {
  flex: 1;
  overflow: hidden;
}

/* Ensure the table header is sticky */
.tracker-table-container > div:nth-child(1) {
  position: sticky;
  top: 0;
  z-index: 20;
  background-color: white;
}

/* Ensure the table footer is sticky */
.tracker-table-container > div:nth-child(3) {
  position: sticky;
  bottom: 0;
  z-index: 20;
  background-color: white;
}

/* Add specific styles for the tracker table scrollbar */
.tracker-table-scroll {
  /* Force scrollbar to be visible */
  overflow: auto !important;
  scrollbar-width: auto !important;
  scrollbar-color: #888 #f1f1f1 !important;
}

.tracker-table-scroll::-webkit-scrollbar {
  width: 10px !important;
  height: 10px !important;
  display: block !important;
  visibility: visible !important;
}

.tracker-table-scroll::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 4px !important;
}

.tracker-table-scroll::-webkit-scrollbar-thumb {
  background: #888 !important;
  border-radius: 4px !important;
}

.tracker-table-scroll::-webkit-scrollbar-thumb:hover {
  background: #555 !important;
}

/* Ensure the table container has proper dimensions */
.tracker-table-container {
  height: 100% !important;
  width: 100% !important;
  position: relative !important;
  overflow: hidden !important;
}
