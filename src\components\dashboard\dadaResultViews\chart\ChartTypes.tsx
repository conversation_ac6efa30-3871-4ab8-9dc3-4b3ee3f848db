
import React from "react";
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  <PERSON>A<PERSON>s, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  Cell,
  LabelList,
  ResponsiveContainer
} from "recharts";
import { RoundedBar, HorizontalRoundedBar } from "./CustomBarShape";
import { formatNumber, generateColor } from "./ChartUtils";

interface ChartProps {
  chartData: any[];
  categoryAxis: string;
  dataColumns: string[];
  queryType: string;
  maxValue: number;
}

export const VerticalBarChart: React.FC<ChartProps> = ({
  chartData,
  categoryAxis,
  dataColumns,
  queryType,
  maxValue
}) => {
  // Validate inputs
  if (!chartData?.length || !categoryAxis || !dataColumns?.length) {
    return <div className="flex items-center justify-center h-full">No chart data available</div>;
  }

  // Ensure we have valid data
  const validData = chartData?.map(item => {
    const newItem = { ...item };
    dataColumns.forEach(col => {
      // Convert any NaN or invalid numbers to 0
      newItem[col] = Number(item[col]);
      if (isNaN(newItem[col]) || !isFinite(newItem[col])) {
        newItem[col] = 0;
      }
    });
    return newItem;
  }) || [];

  // Calculate valid domain
  const validMaxValue = Math.max(
    0,
    ...validData.flatMap(item => 
      dataColumns.map(col => Number(item[col]))
    ).filter(val => !isNaN(val) && isFinite(val))
  );

  const finalMaxValue = Math.max(validMaxValue * 1.1, 1); // Add 10% padding, minimum of 1
  
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={validData}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 40
        }}
        barSize={50}
        maxBarSize={80}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" vertical={false} />
        <XAxis 
          dataKey={categoryAxis}
          tick={{fontSize: 12}}
          interval={0}
          angle={-15}
          textAnchor="end"
          height={60}
        />
        <YAxis 
          tickFormatter={formatNumber}
          fontSize={12}
          domain={[0, finalMaxValue]}
          padding={{ top: 10 }}
        />
        <Tooltip 
          formatter={(value, name) => [
            typeof value === 'number' ? formatNumber(value) : value, 
            name
          ]}
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #f0f0f0',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
          }}
          cursor={{fill: 'rgba(0, 0, 0, 0.05)'}}
        />
        <Legend verticalAlign="top" height={36} />
        {dataColumns.map((column, index) => (
          <Bar 
            key={column} 
            dataKey={column} 
            name={column}
            shape={<RoundedBar radius={4} />}
          >
            {chartData.map((entry, entryIndex) => (
              <Cell 
                key={`cell-${entryIndex}`} 
                fill={generateColor(index, entry[categoryAxis], queryType)} 
              />
            ))}
            <LabelList 
              dataKey={column} 
              position="top" 
              formatter={formatNumber}
              style={{ fontSize: 12, fill: '#333', fontWeight: 'bold' }}
            />
          </Bar>
        ))}
      </BarChart>
    </ResponsiveContainer>
  );
};

export const HorizontalBarChart: React.FC<ChartProps> = ({
  chartData,
  categoryAxis,
  dataColumns,
  maxValue,
  queryType
}) => {
  // Ensure we have valid data
  const validData = chartData?.map(item => {
    const newItem = { ...item };
    dataColumns.forEach(col => {
      // Convert any NaN or invalid numbers to 0
      newItem[col] = Number(item[col]);
      if (isNaN(newItem[col]) || !isFinite(newItem[col])) {
        newItem[col] = 0;
      }
    });
    return newItem;
  }) || [];

  // Calculate valid domain
  const validMaxValue = Math.max(
    0,
    ...validData.flatMap(item => 
      dataColumns.map(col => Number(item[col]))
    ).filter(val => !isNaN(val) && isFinite(val))
  );

  const finalMaxValue = Math.max(validMaxValue * 1.1, 1); // Add 10% padding, minimum of 1

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={validData}
        layout="vertical"
        margin={{
          top: 5,
          right: 30,
          left: 100,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" horizontal={false} />
        <XAxis
          type="number"
          domain={[0, finalMaxValue]}
          tickFormatter={formatNumber}
        />
        <YAxis
          type="category"
          dataKey={categoryAxis}
          width={90}
          tick={{ fontSize: 12 }}
        />
        <Tooltip
          formatter={(value: any) => formatNumber(Number(value))}
          contentStyle={{ fontSize: '12px' }}
        />
        {dataColumns.map((col, index) => (
          <Bar
            key={col}
            dataKey={col}
            fill={generateColor(index, validData[index]?.[categoryAxis], queryType)}
            radius={[0, 4, 4, 0]}
          />
        ))}
      </BarChart>
    </ResponsiveContainer>
  );
};
