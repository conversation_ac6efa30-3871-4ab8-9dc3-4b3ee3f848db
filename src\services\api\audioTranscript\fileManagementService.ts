
import { toast } from 'sonner';
import { store } from '@/stores/store';
import { setError } from '@/stores/errorSlice';
import { FolderType, TranscriptResponse, FileListResponse } from './types';
import { API_KEY, baseUrl } from './config';

export const saveTranscriptToFolder = async (
  fileId: number,
  newFileName: string,
  newFolderType: FolderType
): Promise<boolean> => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }

    const payload = {
      new_filename: newFileName,
      new_folder_type: newFolderType
    };

    const response = await fetch(`${baseUrl}/save-file/${fileId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY || '',
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to save transcript');
    }

    return true;
  } catch (error) {
    console.error('Error saving transcript:', error);
    toast.error('Failed to save transcript', {
      description: error instanceof Error ? error.message : 'Unknown error occurred'
    });
    return false;
  }
};

export const getTranscriptByFileName = async (
  fileName: string,
  folderType: FolderType,
  email?: string,
  username?: string
): Promise<TranscriptResponse | null> => {
  try {
    if (!baseUrl) {
      store.dispatch(setError({
        message: 'API URL is not configured',
        statusCode: 500
      }));
      return null;
    }

    const encodedFileName = encodeURIComponent(fileName);
    const response = await fetch(`${baseUrl}/files/${folderType}/${encodedFileName}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Failed to fetch transcript`);
    }

    const data = await response.json();
    
    if (!data.transcript) {
      throw new Error('Invalid transcript data received');
    }

    const audioUrl = data.audio_url 
      ? (data.audio_url.startsWith('/') ? `${baseUrl}${data.audio_url}` : data.audio_url)
      : '';

    return {
      message: 'Successfully retrieved transcript',
      transcript: data.transcript,
      file_id: data.file_id,
      summary: {
        summary: data.summary?.summary || '',
        actionItems: data.summary?.actionItems || [],
        importantPoints: data.summary?.importantPoints || [],
        openQuestions: data.summary?.openQuestions || []
      },
      audioUrl: audioUrl, // Use the properly formatted audio URL
      email: email,
      username: username
    };
  } catch (error) {
    console.error('Error fetching transcript:', error);
    store.dispatch(setError({
      message: error instanceof Error ? error.message : 'An unexpected error occurred',
      statusCode: 500
    }));
    return null;
  }
};

export const getFilesByFolderType = async (folderType: FolderType): Promise<string[]> => {
  try {
    if (!baseUrl) {
      throw new Error('API URL is not configured');
    }

    const response = await fetch(`${baseUrl}/files/${folderType}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: FileListResponse = await response.json();
    
    if (data.status === 'success') {
      return data.files;
    } else {
      throw new Error('Failed to fetch files');
    }
  } catch (error) {
    console.error(`Error fetching ${folderType} files:`, error);
    throw error;
  }
};

export const deleteFileByFolderType = async (folderType: FolderType, fileName: string) => {
  const response = await fetch(`${baseUrl}/files/${folderType}/${encodeURIComponent(fileName)}`, {
    method: 'DELETE',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to delete file: ${response.statusText}`);
  }
  const data = await response.json();
  return data.message;
};
