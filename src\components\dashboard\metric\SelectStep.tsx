
import React from "react"
import { MetricFormData } from "@/stores/metricSlice"
import { Checkbox } from "@/components/ui/checkbox"

interface SelectStepProps {
  formData: MetricFormData
  handleTableSelect: (tableName: string) => void
}

export const SelectStep: React.FC<SelectStepProps> = ({ formData, handleTableSelect }) => {
  return (
    <div>
      <h3 className="text-sm font-medium text-gray-700 mb-4">Tables:</h3>
      <div className="border border-gray-300 rounded overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr className="bg-lime-300">
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                TableName
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                Columns
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Select</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-700 uppercase tracking-wider">
                Add the metric
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            <tr className="bg-white">
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Table1</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Col1, Col2</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <input
                  type="checkbox"
                  checked={formData.selectedTables.includes("Table1")}
                  onChange={() => handleTableSelect("Table1")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                <input type="checkbox" className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
              </td>
            </tr>
            <tr className="bg-lime-50">
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Table2</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Col2</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <input
                  type="checkbox"
                  checked={formData.selectedTables.includes("Table2")}
                  onChange={() => handleTableSelect("Table2")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                <input type="checkbox" className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
              </td>
            </tr>
            <tr className="bg-white">
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Table3</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Col2,Col3</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <input
                  type="checkbox"
                  checked={formData.selectedTables.includes("Table3")}
                  onChange={() => handleTableSelect("Table3")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                <input type="checkbox" className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  )
}
