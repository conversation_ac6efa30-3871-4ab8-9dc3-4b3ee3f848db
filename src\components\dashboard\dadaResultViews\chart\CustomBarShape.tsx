
import React from 'react';
import { Rectangle } from 'recharts';

interface RoundedBarProps {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  radius?: number;
  fill?: string;
}

// Custom bar shape with rounded corners for vertical bars (rounded on top)
export const RoundedBar: React.FC<RoundedBarProps> = ({
  x = 0,
  y = 0, 
  width = 0, 
  height = 0, 
  radius = 5, 
  fill = '#8884d8'
}) => {
  return (
    <Rectangle
      x={x}
      y={y}
      width={width}
      height={height}
      fill={fill}
      radius={[radius, radius, 0, 0]}
    />
  );
};

// Horizontal rounded bar (rounded on left side)
export const HorizontalRoundedBar: React.FC<RoundedBarProps> = ({
  x = 0,
  y = 0, 
  width = 0, 
  height = 0, 
  radius = 5, 
  fill = '#8884d8'
}) => {
  return (
    <Rectangle
      x={x}
      y={y}
      width={width}
      height={height}
      fill={fill}
      radius={[radius, 0, 0, radius]}
    />
  );
};
