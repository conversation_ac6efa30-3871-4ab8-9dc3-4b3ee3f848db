"use client"

import { useState } from "react"
import { Database } from "lucide-react"
import * as DropdownMenu from "@radix-ui/react-dropdown-menu"

interface DatabaseSelectorProps {
  onDatabaseChange: (database: string) => void
  size?: "small" | "large" | "default"
}

export const DatabaseSelector = ({ onDatabaseChange }: DatabaseSelectorProps) => {
  const [selectedDb, setSelectedDb] = useState("SQL")

  const databases = ["SQL", "PostgreSQL", "Oracle", "Pinecone", "MongoDB"]

  const handleSelect = (db: string) => {
    setSelectedDb(db)
    onDatabaseChange(db)
  }

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <button
          className="flex items-center text-xs text-white bg-blue-500 hover:bg-green-200 hover:text-black rounded px-2 py-1"
        >
          <Database size={12} className="mr-1" />
          <span>{selectedDb}</span>
          <svg
            className="w-3 h-3 ml-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Content
        className="mt-1 w-36 bg-white border border-gray-200 rounded shadow-lg z-10"
        sideOffset={5}
      >
        {databases.map((db) => (
          <DropdownMenu.Item
            key={db}
            onSelect={() => handleSelect(db)}
            className={`block w-full text-left px-3 py-1.5 text-xs cursor-pointer ${
              selectedDb === db ? "bg-gray-100 font-medium" : "hover:bg-gray-50"
            }`}
          >
            {db}
          </DropdownMenu.Item>
        ))}
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  )
}