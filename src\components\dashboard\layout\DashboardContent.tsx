
import React from 'react';

interface DashboardContentProps {
  children: React.ReactNode;
  sidebarCollapsed: boolean;
  rightSidebarVisible: boolean;
}

const DashboardContent: React.FC<DashboardContentProps> = ({ 
  children,
  sidebarCollapsed,
  rightSidebarVisible 
}) => {
  return (
    <div className="flex-1 transition-all duration-300">
      {children}
    </div>
  );
};

export default DashboardContent;
