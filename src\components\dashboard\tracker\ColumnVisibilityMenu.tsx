
import React from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ColumnDef } from '@tanstack/react-table';
import { TrackerItem } from '@/hooks/useTrackerItems';
import { Settings, Eye, EyeOff, RotateCcw } from 'lucide-react';

interface ColumnVisibilityMenuProps {
  columns: ColumnDef<TrackerItem>[];
  columnVisibility: Record<string, boolean>;
  toggleColumnVisibility: (columnId: string) => void;
  showAllColumns: () => void;
  showEssentialOnly: () => void;
  resetToDefaults: () => void;
  visibleColumnCount: number;
  totalColumnCount: number;
}

const ColumnVisibilityMenu: React.FC<ColumnVisibilityMenuProps> = ({
  columns,
  columnVisibility,
  toggleColumnVisibility,
  showAllColumns,
  showEssentialOnly,
  resetToDefaults,
  visibleColumnCount,
  totalColumnCount
}) => {
  // Get column display name
  const getColumnDisplayName = (column: ColumnDef<TrackerItem>) => {
    const columnDef = column as any;
    const id = String(columnDef.accessorKey || column.id);
    
    // Custom display names for better UX
    const displayNames: Record<string, string> = {
      'projectName': 'Project Name',
      'activity': 'Activity',
      'activityType': 'Activity Type',
      'assignedOn': 'Assigned On',
      'activityStatus': 'Status',
      'assignedTo': 'Assigned To',
      'eta': 'ETA',
      'meetingId': 'Meeting ID',
      'comments': 'Comments',
      'tags': 'Tags',
      'history': 'History'
    };
    
    return displayNames[id] || id.charAt(0).toUpperCase() + id.slice(1);
  };

  // Group columns by priority for better organization
  const essentialColumns = columns.filter(column => {
    const columnDef = column as any;
    const id = String(columnDef.accessorKey || column.id);
    return ['activity', 'activityStatus'].includes(id);
  });

  const highPriorityColumns = columns.filter(column => {
    const columnDef = column as any;
    const id = String(columnDef.accessorKey || column.id);
    return ['projectName', 'activityType', 'assignedTo', 'assignedOn'].includes(id);
  });

  const otherColumns = columns.filter(column => {
    const columnDef = column as any;
    const id = String(columnDef.accessorKey || column.id);
    return !['activity', 'activityStatus', 'projectName', 'activityType', 'assignedTo', 'assignedOn'].includes(id);
  });

  const renderColumnGroup = (groupColumns: ColumnDef<TrackerItem>[], groupName: string) => (
    <div className="space-y-1.5">
      <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wide">{groupName}</h5>
      {groupColumns.map(column => {
        const columnDef = column as any;
        const id = String(columnDef.accessorKey || column.id);
        const displayName = getColumnDisplayName(column);
        
        return (
          <div key={id} className="flex items-center space-x-2">
            <Checkbox 
              id={`column-${id}`}
              checked={columnVisibility[id] !== false}
              onCheckedChange={() => toggleColumnVisibility(id)}
            />
            <Label 
              htmlFor={`column-${id}`}
              className="text-sm cursor-pointer flex-1"
            >
              {displayName}
            </Label>
          </div>
        );
      })}
    </div>
  );

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className="flex items-center gap-1"
        >
          <Settings size={14} />
          <span>Columns ({visibleColumnCount}/{totalColumnCount})</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-72 p-3" align="end">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Column Visibility</h4>
            <span className="text-xs text-gray-500">{visibleColumnCount} of {totalColumnCount} visible</span>
          </div>
          
          {/* Quick Actions */}
          <div className="flex gap-1">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={showAllColumns}
              className="flex items-center gap-1 text-xs"
            >
              <Eye size={12} />
              Show All
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={showEssentialOnly}
              className="flex items-center gap-1 text-xs"
            >
              <EyeOff size={12} />
              Essential
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={resetToDefaults}
              className="flex items-center gap-1 text-xs"
            >
              <RotateCcw size={12} />
              Reset
            </Button>
          </div>
          
          <Separator />
          
          {/* Grouped Columns */}
          <div className="space-y-3 max-h-80 overflow-y-auto">
            {renderColumnGroup(essentialColumns, 'Essential')}
            {renderColumnGroup(highPriorityColumns, 'Main')}
            {renderColumnGroup(otherColumns, 'Additional')}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default ColumnVisibilityMenu;
